<template>
  <div class="container">
    <div class="canvas-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p>正在加载流程图...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-overlay">
        <div class="error-content">
          <p>加载失败: {{ error }}</p>
          <button @click="loadBpmnDiagram" class="retry-btn">重试</button>
        </div>
      </div>

      <!-- 流程图画布 -->
      <div class="canvas" ref="canvasRef" />
    </div>
    <div class="controls">
      <button @click="zoomIn" title="放大"><svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64z"></path></svg></button>
      <button @click="zoomOut" title="缩小"><svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64"></path></svg></button>
      <button @click="resetView" title="重置视图"><svg  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="m160 96.064 192 .192a32 32 0 0 1 0 64l-192-.192V352a32 32 0 0 1-64 0V96h64zm0 831.872V928H96V672a32 32 0 1 1 64 0v191.936l192-.192a32 32 0 1 1 0 64zM864 96.064V96h64v256a32 32 0 1 1-64 0V160.064l-192 .192a32 32 0 1 1 0-64l192-.192zm0 831.872-192-.192a32 32 0 0 1 0-64l192 .192V672a32 32 0 1 1 64 0v256h-64z"></path></svg></button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount, nextTick } from 'vue';
import BpmnViewer from 'bpmn-js/lib/NavigatedViewer';
import { http } from '@/utils/http';

import 'bpmn-js/dist/assets/diagram-js.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';

// Props 定义 - 由于参数固定，可以简化或保留用于扩展
interface Props {
  processId?: string;
}

const props = defineProps<Props>();

const canvasRef = ref<HTMLDivElement | null>(null);
let viewer: BpmnViewer | null = null;

// 添加加载状态
const loading = ref(false);
const error = ref('');
// const randomKey = ref<number>(Math.random()* Math.random()*Math.random()*100000);

// 获取流程图数据的接口
const getProcessInfo = async () => {
  try {
    const response = await http.request<{
      status: string;
      msg: string;
      data: {
        jsonXml: string;
      };
    }>(
      'get',
      '/eam-pm/process/info?id=changeProcess'
    );

    if (response.status === '0' && response.data?.jsonXml) {
      return response.data.jsonXml;
    } else {
      throw new Error(response.msg || '获取流程图数据失败');
    }
  } catch (error) {
    console.error('Failed to fetch process info:', error);
    throw error;
  }
};

// 加载流程图
const loadBpmnDiagram = async () => {
  if (!viewer) {
    console.warn('BpmnFlowChart: viewer 未初始化，等待初始化后重试');
    // 等待一段时间后重试
    setTimeout(() => {
      if (viewer) {
        loadBpmnDiagram();
      }
    }, 100);
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    console.log('BpmnFlowChart: 开始加载流程图数据');
    const xmlData = await getProcessInfo();
    // console.log('BpmnFlowChart: 获取到的 XML 数据:', xmlData);

    if (!xmlData) {
      throw new Error('获取到的流程图数据为空');
    }

    console.log('BpmnFlowChart: 开始导入 XML 数据');
    await viewer.importXML(xmlData);

    // 使用 nextTick 确保 DOM 更新后再调整视图
    await nextTick();
    resetView(); // 初始加载后适应视图

    console.log('BpmnFlowChart: 流程图加载成功');
  } catch (err) {
    const errorMsg = err instanceof Error ? err.message : '加载流程图失败';
    console.error('BpmnFlowChart: 加载失败:', errorMsg);
    error.value = errorMsg;
  } finally {
    loading.value = false;
  }
};

// 初始化 BPMN 查看器
const initViewer = () => {
  if (!canvasRef.value) return;

  viewer = new BpmnViewer({
    container: canvasRef.value,
  });
};

// 销毁查看器
const destroyViewer = () => {
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
};

// 缩放控制方法
const zoomIn = () => {
  if (viewer) {
    viewer.get('zoomScroll').stepZoom(1); // 放大一级
  }
};

const zoomOut = () => {
  if (viewer) {
    viewer.get('zoomScroll').stepZoom(-1); // 缩小一级
  }
};

// 重置视图 - 关键方法
const resetView = () => {
  if (viewer) {
    const canvas = viewer.get('canvas');

    // 使用 'fit-viewport' 并提供 50px 的内边距
    // 这会让图形在缩放后四周留有空白，从而实现完美的视觉居中
    canvas.zoom('fit-viewport', {
      padding: 50
    });
  }
};

// 强制刷新流程图
const forceRefresh = async () => {
  console.log('BpmnFlowChart: 强制刷新流程图');
  if (viewer) {
    viewer.clear();
  }
  await loadBpmnDiagram();
};

// 移除 processId 监听，完全依赖外部手动触发
// watch(() => props.processId, () => {
//   if (viewer) {
//     forceRefresh();
//   }
// }, { immediate: false });

onMounted(async () => {
  console.log('BpmnFlowChart: 组件挂载');

  // 确保 DOM 完全渲染后再初始化
  await nextTick();

  if (!canvasRef.value) {
    console.error('BpmnFlowChart: canvas 容器未找到');
    return;
  }

  initViewer();

  // 移除自动加载，完全依赖外部触发
  console.log('BpmnFlowChart: viewer 初始化完成，等待外部触发加载');
});

onBeforeUnmount(() => {
  destroyViewer();
});

// 暴露方法给父组件
defineExpose({
  loadBpmnDiagram,
  forceRefresh,
  zoomIn,
  zoomOut,
  resetView
});
</script>

<style scoped>
.container {
  position: relative;
  height: 600px;
  width: 100% !important;
  max-width: 100% !important;
}

.canvas-container {
  height: 100%;
  width: 100%;
}

.canvas {
  height: 100%;
  width: 100%;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
}

.controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.controls button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f8f8f8;
  border: 1px solid #ccc;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: all 0.2s;
  color: #000;
  padding: 6px;
}

.controls button:hover {
  background-color: #e0e0e0;
}

.controls i {
  font-size: 20px;
}

/* 移除 bpmn.io logo */
:deep(.bjs-powered-by) {
  display: none;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(249, 249, 249, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(249, 249, 249, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.error-content {
  text-align: center;
  color: #f56c6c;
}

.retry-btn {
  margin-top: 10px;
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-btn:hover {
  background: #66b1ff;
}
</style>
