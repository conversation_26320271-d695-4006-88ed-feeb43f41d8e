<template>
  <el-dialog
    :model-value="visible"
    title="编辑变更记录"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="emit('update:visible', $event)"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
    >
      <!-- 第一行：变更类型、变更单编号 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="变更类型" prop="changeType">
            <el-input
              v-model="formData.changeType"
              placeholder="请输入变更类型"
              maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="变更单编号" prop="changeNum">
            <el-input
              v-model="formData.changeNum"
              placeholder="请输入变更单编号"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：变更单标题 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="变更单名称" prop="changeTitle">
            <el-input
              v-model="formData.changeTitle"
              placeholder="请输入变更单标题"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：项目专业、合同名称 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目专业名称" prop="professionalName">
            <el-input
              v-model="formData.professionalName"
              placeholder="请输入项目专业名称"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同名称" prop="contractName">
            <el-input
              v-model="formData.contractName"
              placeholder="请输入合同名称"
              maxlength="200"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行：合同编号、调整合同金额 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同编号" prop="contractNumber">
            <el-input
              v-model="formData.contractNumber"
              placeholder="请输入合同编号"
              maxlength="100"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同变更金额(元)" prop="adjustedAmount">
            <el-input-number
              v-model="formData.adjustedAmount"
              placeholder="请输入合同变更金额"
              :precision="2"
              :step="100"
              :min="0"
              :max="999999999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第五行：变更事由 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="变更事由" prop="changeReason">
            <el-input
              v-model="formData.changeReason"
              placeholder="请输入变更事由"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="变更内容说明" prop="changeDescription">
        <el-input
          v-model="formData.changeDescription"
          type="textarea"
          placeholder="请输入变更内容说明"
          :rows="6"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSave">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { saveChange, type ChangeData } from './api';

// Props
interface Props {
  visible: boolean;
  changeData?: ChangeData | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean];
  'success': [];
}>();

// 响应式数据
const formRef = ref<FormInstance>();
const saving = ref(false);

// 表单数据
const formData = reactive({
  id: null as number | null,
  projectId: '',
  projectCode: '',
  projectName: '',
  professionalCode: '',
  professionalName: '',
  changeTitle: '',
  changeNum: '',
  changeReason: '',
  contractNumber: '',
  contractName: '',
  changeType: '',
  adjustedAmount: null as number | null, // 虽然接口文档说是string，但表单输入使用number更合适
  changeDescription: '',
  createdBy: '',
  createdTime: '',
  updatedBy: '',
  updatedTime: '',
  fileId: '',
  pageNum: 0,
  pageSize: 0
});

// 表单验证规则
const formRules: FormRules = {
  changeType: [
    { max: 50, message: '变更类型不能超过50个字符', trigger: 'blur' }
  ],
  changeNum: [
    { max: 100, message: '变更单编号不能超过100个字符', trigger: 'blur' }
  ],
  changeTitle: [
    { required: true, message: '请输入变更单标题', trigger: 'blur' },
    { max: 200, message: '变更单标题不能超过200个字符', trigger: 'blur' }
  ],
  professionalName: [
    { max: 100, message: '项目专业名称不能超过100个字符', trigger: 'blur' }
  ],
  contractName: [
    { max: 200, message: '合同名称不能超过200个字符', trigger: 'blur' }
  ],
  contractNumber: [
    { max: 100, message: '合同编号不能超过100个字符', trigger: 'blur' }
  ],
  adjustedAmount: [
    { type: 'number', message: '请输入有效的金额', trigger: 'blur' }
  ],
  changeReason: [
    { max: 500, message: '变更事由不能超过500个字符', trigger: 'blur' }
  ],
  changeDescription: [
    { max: 2000, message: '变更内容说明不能超过2000个字符', trigger: 'blur' }
  ]
};

// 监听弹窗显示状态，初始化表单数据
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.changeData) {
    // 复制数据到表单
    Object.keys(formData).forEach(key => {
      if (props.changeData && key in props.changeData) {
        formData[key] = props.changeData[key];
      }
    });
  }
});

// 处理关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  // 重置表单
  formRef.value?.resetFields();
};

// 处理保存
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    // 表单验证
    await formRef.value.validate();

    saving.value = true;

    // 调用保存接口
    const result = await saveChange(formData);

    if (result.success) {
      ElMessage.success(result.message);
      emit('success');
      handleClose();
    }
  } catch (error) {
    console.error('保存变更记录失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

// 设置组件名称
defineOptions({
  name: 'EditChangeDialog'
});
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.9);
}

:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    border-color: #409eff;
  }

  &.is-focus {
    border-color: #409eff;
  }
}

:deep(.el-input__inner) {
  color: #ffffff;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
}

:deep(.el-textarea__inner) {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:hover {
    border-color: #409eff;
  }

  &:focus {
    border-color: #409eff;
  }
}

:deep(.el-input-number) {
  width: 100%;

  .el-input__wrapper {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover {
      border-color: #409eff;
    }

    &.is-focus {
      border-color: #409eff;
    }
  }

  .el-input__inner {
    color: #ffffff;

    &::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
  }
}
</style>
