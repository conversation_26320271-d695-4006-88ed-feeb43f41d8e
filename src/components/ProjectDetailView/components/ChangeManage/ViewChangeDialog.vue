<template>
  <el-dialog
    :model-value="visible"
    title="变更记录详情"
    width="900px"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    @update:model-value="emit('update:visible', $event)"
    @close="handleClose"
  >
    <div class="change-detail-content" v-if="changeData">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20" class="detail-row">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">变更类型：</span>
              <span class="value">{{ changeData.changeType || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">变更单编号：</span>
              <span class="value">{{ changeData.changeNum || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <div class="detail-item full-width">
          <span class="label">变更单标题：</span>
          <span class="value">{{ changeData.changeTitle || '-' }}</span>
        </div>
      </div>

      <!-- 项目信息 -->
      <div class="detail-section">
        <h4 class="section-title">项目信息</h4>
        <el-row :gutter="20" class="detail-row">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">项目名称：</span>
              <span class="value">{{ changeData.projectName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">项目编码：</span>
              <span class="value">{{ changeData.projectCode || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <div class="detail-item">
          <span class="label">项目专业：</span>
          <span class="value">{{ changeData.professionalName || '-' }}</span>
        </div>
      </div>

      <!-- 合同信息 -->
      <div class="detail-section">
        <h4 class="section-title">合同信息</h4>
        <el-row :gutter="20" class="detail-row">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">合同名称：</span>
              <span class="value">{{ changeData.contractName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">合同编号：</span>
              <span class="value">{{ changeData.contractNumber || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <div class="detail-item">
          <span class="label">合同变更金额：</span>
          <span class="value amount">{{ formatAmount(changeData.adjustedAmount) }}</span>
        </div>
      </div>

      <!-- 变更详情 -->
      <div class="detail-section">
        <h4 class="section-title">变更详情</h4>
        <div class="detail-item full-width">
          <span class="label">变更事由：</span>
          <span class="value">{{ changeData.changeReason || '-' }}</span>
        </div>
        
        <div class="detail-item full-width">
          <span class="label">变更内容说明：</span>
          <div class="value description">
            {{ changeData.changeDescription || '-' }}
          </div>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="detail-section">
        <h4 class="section-title">时间信息</h4>
        <el-row :gutter="20" class="detail-row">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatTime(changeData.createdTime) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">更新时间：</span>
              <span class="value">{{ formatTime(changeData.updatedTime) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" class="detail-row">
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">创建人：</span>
              <span class="value">{{ changeData.createdBy || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="label">更新人：</span>
              <span class="value">{{ changeData.updatedBy || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { type ChangeData } from './api';

// Props
interface Props {
  visible: boolean;
  changeData?: ChangeData | null;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:visible': [visible: boolean];
}>();

// 处理关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
};

// 格式化金额
const formatAmount = (amount: number | string | null | undefined): string => {
  if (amount === null || amount === undefined || amount === '') {
    return '-';
  }
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(num)) {
    return '-';
  }
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }) + ' 元';
};

// 格式化时间
const formatTime = (time: string | null | undefined): string => {
  if (!time) return '-';
  try {
    const date = new Date(time);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch {
    return time;
  }
};

// 设置组件名称
defineOptions({
  name: 'ViewChangeDialog'
});
</script>

<style scoped lang="scss">
.change-detail-content {
  max-height: 600px;
  // overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row {
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  
  &.full-width {
    width: 100%;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  flex-shrink: 0;
  width: 120px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  line-height: 1.5;
}

.value {
  flex: 1;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  word-break: break-all;
  
  &.amount {
    color: #67c23a;
    font-weight: 600;
  }
  
  &.description {
    white-space: pre-wrap;
    background-color: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 8px;
    max-height: 200px;
    overflow-y: auto;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

// 滚动条样式
.change-detail-content::-webkit-scrollbar,
.description::-webkit-scrollbar {
  width: 6px;
}

.change-detail-content::-webkit-scrollbar-track,
.description::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.change-detail-content::-webkit-scrollbar-thumb,
.description::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}
</style>
