// 变更管理相关的API接口和数据类型定义
import { http } from '@/utils/http';

// 根据接口文档定义的变更数据类型
export interface ChangeData {
  id: number;
  projectId?: string;
  projectCode?: string;
  projectName?: string;
  professionalCode?: string;
  professionalName?: string;
  changeTitle?: string;
  changeNum?: string;
  changeReason?: string;
  contractNumber?: string;
  contractName?: string;
  changeType?: string;
  adjustedAmount?: number;
  changeDescription?: string;
  createdBy?: string;
  createdTime?: string;
  updatedBy?: string;
  updatedTime?: string;
  fileId?: string;
  fileName?: string;
  pageNum?: number;
  pageSize?: number;

  // 兼容旧字段名，用于表格显示
  changeNumber?: string;
  changeCategory?: string;
  changeName?: string;
  contractAmount?: number;
  changeAttachment?: string;
  details?: string;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

// 查询参数接口
export interface ChangeQueryParams {
  pageNum: number;
  pageSize: number;
  projectCode: string;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

// API响应接口
export interface ApiResponse<T> {
  status: string;
  msg: string;
  data: T;
}

// 分页响应数据
export interface PageResponse<T> {
  total: number;
  list: T[];
  pageNum: number;
  pageSize: number;
  size: number;
  startRow: number;
  endRow: number;
  pages: number;
  prePage: number;
  nextPage: number;
  isFirstPage: boolean;
  isLastPage: boolean;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  navigatePages: number;
  navigatepageNums: number[];
  navigateFirstPage: number;
  navigateLastPage: number;
}

export interface ChangeListResponse {
  rows: ChangeData[];
  total: number;
  success: boolean;
}

/**
 * 将API返回的数据转换为表格显示的数据格式
 */
const convertApiDataToTableData = (apiData: ChangeData[]): ChangeData[] => {
  return apiData.map(item => ({
    ...item,
    // 映射字段以兼容表格显示
    changeNumber: item.changeNum || '',
    changeCategory: item.changeType || '',
    changeName: item.changeTitle || '',
    contractAmount: item.adjustedAmount || 0,
    changeAttachment: item.fileName || '',
    details: item.changeDescription || ''
  }));
};

/**
 * 获取变更列表 - 对接真实接口
 * @param params 查询参数
 */
export const getChangeList = async (params: ChangeQueryParams): Promise<ChangeListResponse> => {
  try {
    const requestParams = {
      pageNum: params.pageNum.toString(),
      pageSize: params.pageSize.toString(),
      projectCode: params.projectCode
    };

    console.log('变更管理API请求参数:', requestParams);

    const response = await http.request<ApiResponse<PageResponse<ChangeData>>>(
      "post",
      "/changeOrder/list",
      { data: requestParams }
    );

    console.log('变更管理API响应:', response);

    if (response.status === '0' && response.data) {
      const convertedData = convertApiDataToTableData(response.data.list);
      return {
        rows: convertedData,
        total: response.data.total,
        success: true
      };
    } else {
      throw new Error(response.msg || '获取变更列表失败');
    }
  } catch (error) {
    console.error('获取变更列表失败:', error);
    throw error;
  }
};

/**
 * 删除变更记录 - 暂未实现
 * @param changeId 变更ID
 */
export const deleteChange = async (changeId: number): Promise<{ success: boolean }> => {
  // TODO: 对接真实删除接口
  console.log('删除变更记录:', changeId);
  return { success: true };
};

/**
 * 批量删除变更记录 - 暂未实现
 * @param changeIds 变更ID数组
 */
export const batchDeleteChanges = async (changeIds: number[]): Promise<{ success: boolean }> => {
  // TODO: 对接真实批量删除接口
  console.log('批量删除变更记录:', changeIds);
  return { success: true };
};

/**
 * 导出变更数据 - 暂未实现
 * @param changeIds 变更ID数组，为空时导出全部
 */
export const exportChanges = async (changeIds?: number[]): Promise<{ success: boolean; downloadUrl?: string }> => {
  // TODO: 对接真实导出接口
  console.log('导出变更数据:', changeIds);
  return {
    success: true,
    downloadUrl: '/api/download/changes.xlsx'
  };
};
