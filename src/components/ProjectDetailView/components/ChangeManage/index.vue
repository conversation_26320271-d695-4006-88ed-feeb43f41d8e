<template>
  <div class="change-manage-container">
    <!-- 变更列表 -->
    <div class="change-list-section">
      <div class="section-header">
        <div class="section-title">变更列表</div>
      </div>

      <im-table
        ref="changeTableRef"
        :data="changeList"
        :columns="changeColumns"
        :pagination="paginationConfig"
        :loading="loading"
        :toolbar="toolbarConfig"
        :column-storage="createColumnStorage('change_manage', 'local')"
        height="616px"
        stripe
        border
        center
        show-index
        show-overflow-tooltip
        @on-page-change="handlePageChange"
        @on-page-size-change="handlePageSizeChange"
        @on-reload="handleReload"
        @sort-change="handleSortChange"
        @selection-change="handleSelectionChange"
      >
        <!-- 工具栏左侧 -->
        <!-- <template #toolbar-left="{ checkedRows }">
          <el-button
            type="primary"
            size="small"
            :disabled="checkedRows.length === 0"
            @click="handleBatchExport(checkedRows)"
          >
            批量导出
          </el-button>
          <el-button
            type="danger"
            size="small"
            :disabled="checkedRows.length === 0"
            @click="handleBatchDelete(checkedRows)"
          >
            批量删除
          </el-button>
        </template> -->

        <!-- 工具栏右侧 -->
        <!-- <template #toolbar-right>
          <el-button
            type="success"
            size="small"
            @click="handleAddChange"
          >
            新增变更
          </el-button>
          <el-button
            size="small"
            @click="handleExportAll"
          >
            导出全部
          </el-button>
        </template> -->

        <!-- 操作列 -->
        <template #operator="{ row }">
          <el-button
            type="primary"
            size="small"
            link
            @click="handleViewDetail(row)"
          >
            修改
          </el-button>
          <el-button
            type="warning"
            size="small"
            link
            @click="handleEdit(row)"
          >
            详情
          </el-button>
        </template>

        <!-- 合同金额列 -->
        <template #contractAmount="{ row }">
          {{ formatAmount(row.adjustedAmount) }}
        </template>
      </im-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { ImTableColumnProps } from '@/components/ItsmCommon';
import { createColumnStorage } from '@/components/ItsmCommon';
import type { ChangeData } from './api';

// Props 接口定义
interface Props {
  changeList: ChangeData[];
  pagination: {
    currentPage: number;
    pageSize: number;
    total: number;
  };
  loading?: boolean;
  height?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: '450px'
});

// Emits 定义
const emit = defineEmits<{
  'page-change': [page: number, pageSize: number];
  'sort-change': [sortField: string, sortOrder: string];
  'selection-change': [selectedRows: ChangeData[]];
  'view-detail': [row: ChangeData];
  'edit': [row: ChangeData];
  'delete': [row: ChangeData];
  'batch-delete': [rows: ChangeData[]];
  'batch-export': [rows: ChangeData[]];
  'export-all': [];
  'add-change': [];
  'reload': [];
}>();

// 响应式数据
const changeTableRef = ref();

// 工具栏配置
const toolbarConfig = ref(true);

// 分页配置
const paginationConfig = computed(() => ({
  ...props.pagination,
  background: true,
  layout: 'total, sizes, prev, pager, next, jumper',
  pageSizes: [10, 20, 50, 100],
  hideOnEmptyData: false,
  align: 'right' as const
}));

// 表格列配置 - 根据接口文档调整字段
const changeColumns = ref<ImTableColumnProps<ChangeData>[]>([
  {
    prop: 'changeType',
    label: '变更类型',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    hidden: false
  },
  {
    prop: 'changeNum',
    label: '变更单编号',
    minWidth: 180,
    align: 'center',
    showOverflowTooltip: true,
    hidden: false
  },
  {
    prop: 'changeTitle',
    label: '变更单标题',
    minWidth: 300,
    align: 'left',
    showOverflowTooltip: true,
    sortable: true,
    hidden: false
  },
  {
    prop: 'professionalName',
    label: '项目专业',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    hidden: false
  },
  {
    prop: 'contractName',
    label: '合同名称',
    minWidth: 200,
    align: 'left',
    showOverflowTooltip: true,
    hidden: false
  },
  {
    prop: 'adjustedAmount',
    label: '调整合同金额（元）',
    minWidth: 160,
    align: 'right',
    sortable: true,
    showOverflowTooltip: true,
    slot: 'contractAmount',
    hidden: false
  },
  {
    prop: 'changeDescription',
    label: '变更内容说明',
    minWidth: 200,
    align: 'left',
    showOverflowTooltip: true,
    hidden: false
  },
  {
    prop: 'operator',
    label: '操作',
    minWidth: 120,
    align: 'center',
    slot: 'operator',
    fixed: 'right',
    hidden: false
  }
]);

// 格式化金额
const formatAmount = (amount: string | number) => {
  if (!amount) return '0.00';
  const num = typeof amount === 'string' ? parseFloat(amount.replace(/,/g, '')) : amount;
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 事件处理方法 - 统一在page-change中处理分页变化
const handlePageChange = (pageOrSize: number, currentPageSize: number, query?: any) => {
  console.log('ChangeManage - 分页变化:', { pageOrSize, currentPageSize, query });
  // 直接传递给父组件处理
  emit('page-change', pageOrSize, currentPageSize);
};

const handlePageSizeChange = (currentPage: number, size: number, query?: any) => {
  console.log('ChangeManage - 分页大小变化:', { currentPage, size, query });
  // 这个事件现在主要由page-change统一处理，这里保留是为了兼容性
  emit('page-change', size, props.pagination.pageSize);
};

const handleReload = () => {
  console.log('ChangeManage - 刷新数据');
  emit('reload');
};

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  emit('sort-change', prop, order);
};

const handleSelectionChange = (selectedRows: ChangeData[]) => {
  emit('selection-change', selectedRows);
};

const handleViewDetail = (row: ChangeData) => {
  emit('view-detail', row);
};

const handleEdit = (row: ChangeData) => {
  emit('edit', row);
};

const handleDelete = async (row: ChangeData) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除变更"${row.changeTitle || row.changeName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    emit('delete', row);
  } catch {
    // 用户取消删除
  }
};

// 暂时保留这些函数以避免模板错误，但标记为未使用
const handleBatchDelete = async (rows: ChangeData[]) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${rows.length} 个变更记录吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    emit('batch-delete', rows);
  } catch {
    // 用户取消删除
  }
};

const handleBatchExport = (rows: ChangeData[]) => {
  if (rows.length === 0) {
    ElMessage.warning('请先选择要导出的变更记录');
    return;
  }
  emit('batch-export', rows);
};

const handleExportAll = () => {
  emit('export-all');
};

const handleAddChange = () => {
  emit('add-change');
};

// 组件方法暴露
const getTableInstance = () => {
  return changeTableRef.value;
};

const refreshTable = () => {
  changeTableRef.value?.reload();
};

const clearSelection = () => {
  changeTableRef.value?.clearSelection();
};

defineExpose({
  getTableInstance,
  refreshTable,
  clearSelection
});

// 设置组件名称
defineOptions({
  name: 'ChangeManage'
});
</script>

<style scoped lang="scss">
.change-manage-container {
  .change-list-section {
    .section-header {
      margin-bottom: 16px;

      .section-title {
        color: var(--el-color-primary);
        font-size: 14px;
        font-weight: 400;
        padding-left: 8px;
        border-left: 3px solid var(--el-color-primary);
      }
    }
  }
}
</style>
