<template>
  <div class="project-detail-view-container">
    <!-- 头部区域 -->
    <div class="detail-header">
      <div class="header-left">
        <el-icon class="header-icon" color="#409EFF">
          <Document />
        </el-icon>
        <span class="header-title">项目信息</span>
      </div>
      <div class="header-right">
        <el-button size="default" type="primary" @click="handleExportInfo">
          导出项目信息
        </el-button>
        <el-button size="default" @click="handleBack">
          返回
        </el-button>
      </div>
    </div>

    <!-- 项目标题 -->
    <div class="project-title">
      <el-icon class="title-icon" color="#409EFF">
        <Document />
      </el-icon>
      <span class="title-text">{{ projectData.projectName || '项目名称未加载' }}<el-tag
        v-if="projectData.proState"
        :type="getProjectStageTagType(projectData.proState)"
        size="default"
        effect="dark"
        class="project-stage-tag"
      >
        {{ projectData.proState }}
      </el-tag></span>

    </div>

    <!-- 标签页 -->
    <div class="tabs-container">
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="项目概况" name="overview">
          <!-- 项目概况内容 -->
          <div class="overview-section" v-loading="overviewLoading" element-loading-text="正在加载项目详情...">
            <!-- 统计卡片 -->
            <div class="stats-cards">
              <div class="stats-card">
                <div class="stats-value">{{ formatAmount(projectData.availableBudget) }}</div>
                <div class="stats-label">可用批复投资</div>
              </div>
              <div class="stats-card">
                <div class="stats-value">{{ formatAmount(projectData.designBudget) }}</div>
                <div class="stats-label">设计批复投资</div>
              </div>
              <div class="stats-card">
                <div class="stats-value">{{ formatAmount(projectData.currentBudget) }}</div>
                <div class="stats-label">当前合同金额</div>
              </div>
              <div class="stats-card">
                <div class="stats-value">{{ formatAmount(projectData.newBudget) }}</div>
                <div class="stats-label">最新变更金额</div>
              </div>
              <div class="stats-card">
                <div class="stats-value">{{ formatAmount(projectData.totalBudget) }}</div>
                <div class="stats-label">项目差异金额</div>
              </div>
            </div>

            <!-- 主要内容区域 - 左右布局 -->
            <div class="main-content-layout">
              <!-- 左侧项目信息 -->
              <div class="left-content">

                <!-- 项目信息 -->
                <div class="project-info-section">
                  <div class="section-header">
                    <div class="section-title">项目信息</div>
                  </div>

                  <div class="info-grid">
                    <!-- 第一行：项目名称 -->
                    <div class="info-row single-row">
                      <div class="info-item full-width">
                        <span class="info-label">项目名称：</span>
                        <span class="info-value">{{ projectData.projectName }}</span>
                      </div>
                    </div>

                    <!-- 第二行：项目编码、立项批复文号 -->
                    <div class="info-row">
                      <div class="info-item">
                        <span class="info-label">项目编码：</span>
                        <span class="info-value">{{ projectData.projectCode }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">立项批复文号：</span>
                        <span class="info-value">{{ projectData.lxSn || projectData.approvalNumber || '-' }}</span>
                      </div>
                    </div>

                    <!-- 第三行：建设方式、需求单位、建设性质 -->
                    <div class="info-row three-columns">
                      <div class="info-item">
                        <span class="info-label">建设方式：</span>
                        <span class="info-value">{{ projectData.isPurchase || projectData.constructionMethod || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">需求单位：</span>
                        <span class="info-value">{{ projectData.remandUnit || projectData.constructionUnit || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">建设性质：</span>
                        <span class="info-value">{{ projectData.bulidScene || projectData.buildScene || projectData.constructionNature || '-' }}</span>
                      </div>
                    </div>

                    <!-- 第四行：投资主体、项目当前阶段、项目审批方式 -->
                    <div class="info-row three-columns">
                      <div class="info-item">
                        <span class="info-label">投资主体：</span>
                        <span class="info-value">{{ projectData.touziSubject || projectData.fundingSource || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">项目当前阶段：</span>
                        <span class="info-value">{{ projectData.projectState || projectData.proState || projectData.currentStage || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">项目审批方式：</span>
                        <span class="info-value">{{ projectData.projectTrial || projectData.constructionUnitMethod || '-' }}</span>
                      </div>
                    </div>

                    <!-- 第五行：项目管理类别、项目专项标识、立项批复总投资 -->
                    <div class="info-row three-columns">
                      <div class="info-item">
                        <span class="info-label">项目管理类别：</span>
                        <span class="info-value">{{ projectData.manageType || projectData.projectType || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">项目专项标识：</span>
                        <span class="info-value">{{ projectData.postDisaster || projectData.isGeneralContract || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">立项批复总投资（万元）：</span>
                        <span class="info-value">{{ formatAmount(projectData.lxTouZi || projectData.availableBudget) }}</span>
                      </div>
                    </div>

                    <!-- 第六行：项目经理、项目经理邮箱、法人主体 -->
                    <div class="info-row three-columns">
                      <div class="info-item">
                        <span class="info-label">项目经理：</span>
                        <span class="info-value">{{ projectData.managerName || projectData.projectManager || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">项目经理邮箱：</span>
                        <span class="info-value">{{ projectData.managerEmail || projectData.projectManagerMethod || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">法人主体：</span>
                        <span class="info-value">{{ projectData.legalSubject || projectData.basicConstructionCode || '-' }}</span>
                      </div>
                    </div>

                    <!-- 第七行：网络层面、标准建设部门、实际部门 -->
                    <div class="info-row three-columns">
                      <div class="info-item">
                        <span class="info-label">网络层面：</span>
                        <span class="info-value">{{ projectData.networkLevel || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">标准建设部门：</span>
                        <span class="info-value">{{ projectData.disDept || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">实际部门：</span>
                        <span class="info-value">{{ projectData.projectPolicy || '-' }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 主要规模信息 -->
                  <div class="construction-content">
                    <div class="content-row">
                      <span class="content-label">立项建设规模信息：</span>
                      <span class="content-text">{{ projectData.lxBulidContent || projectData.constructionContent || '-' }}</span>
                    </div>
                  </div>

                  <!-- 主要建设内容富文本框 -->
                  <div class="construction-details">
                    <div class="details-row">
                      <span class="details-label">主要建设内容：</span>
                      <el-input
                        v-model="constructionDetailsText"
                        style="width: 100%; max-width: 800px"
                        :rows="6"
                        type="textarea"
                        placeholder="主要建设内容详情"
                        readonly
                        disabled
                      />
                    </div>
                  </div>

                  <!-- 设计信息 -->
                  <div class="design-info-section">
                    <div class="section-header">
                      <div class="section-title">设计信息</div>
                    </div>

                    <div class="info-grid">
                      <!-- 第一行：设计批复文号、设计批复总投资、设计建设规模信息 -->
                      <div class="info-row three-columns">
                        <div class="info-item">
                          <span class="info-label">设计批复文号：</span>
                          <span class="info-value">{{ projectData.sjSn || designInfo.designApprovalNumber || '-' }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">设计批复总投资（万元）：</span>
                          <span class="info-value">{{ formatAmount(projectData.sjTouZi || projectData.designBudget || designInfo.designApprovalAmount) }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">设计批复时间：</span>
                          <span class="info-value">{{ projectData.sjDate || designInfo.designApprovalDate || '-' }}</span>
                        </div>
                      </div>

                      <!-- 第二行：设计建设规模信息 -->
                      <div class="info-row single-row">
                        <div class="info-item full-width">
                          <span class="info-label">设计建设规模信息：</span>
                          <span class="info-value">{{ projectData.sjBulidContent || '-' }}</span>
                        </div>
                      </div>

                      <!-- 第三行：可研批复信息 -->
                      <div class="info-row three-columns">
                        <div class="info-item">
                          <span class="info-label">可研批复文号：</span>
                          <span class="info-value">{{ projectData.kySn || '-' }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">可研批复总投资（万元）：</span>
                          <span class="info-value">{{ formatAmount(projectData.kyTouZi || projectData.currentBudget) }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">可研批复时间：</span>
                          <span class="info-value">{{ projectData.kyDate || '-' }}</span>
                        </div>
                      </div>

                      <!-- 第四行：验收信息 -->
                      <div class="info-row three-columns">
                        <div class="info-item">
                          <span class="info-label">初验时间：</span>
                          <span class="info-value">{{ projectData.firstAccDate || '-' }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">初验验收总投资（万元）：</span>
                          <span class="info-value">{{ formatAmount(projectData.firstAccTouzi || projectData.totalBudget) }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">终验时间：</span>
                          <span class="info-value">{{ projectData.finalAccDate || '-' }}</span>
                        </div>
                      </div>

                      <!-- 第五行：终验验收总投资 -->
                      <div class="info-row three-columns">
                        <div class="info-item">
                          <span class="info-label">终验验收总投资（万元）：</span>
                          <span class="info-value">{{ formatAmount(projectData.finalAccTouzi || projectData.newBudget) }}</span>
                        </div>
                        <div class="info-item">
                          <!-- 空白项 -->
                        </div>
                        <div class="info-item">
                          <!-- 空白项 -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧项目阶段 -->
              <div class="right-content">
                <div class="project-stages-section">
                  <div class="section-header">
                    <div class="section-title">项目阶段</div>
                    <div class="stage-toggle">
                      <el-radio-group v-model="stageViewMode" size="small">
                        <el-radio-button label="planned">计划</el-radio-button>
                        <el-radio-button label="actual">实际</el-radio-button>
                      </el-radio-group>
                    </div>
                  </div>

                  <div class="stages-timeline">
                    <div
                      v-for="(stage, index) in currentStageData"
                      :key="`${stageViewMode}-${index}`"
                      class="stage-item"
                      :class="stage.status"
                    >
                      <div class="stage-indicator">
                        <div class="stage-dot" :class="getStageIndicatorClass(stage.status)"></div>
                        <div v-if="index < currentStageData.length - 1" class="stage-line"></div>
                      </div>
                      <div class="stage-content">
                        <!-- 阶段名称 -->
                        <div class="stage-title-row">
                          <div
                            v-if="!stage.isEditingTitle"
                            class="stage-title"
                            @click="startEditTitle(index)"
                          >
                            {{ stage.stage }}
                            <!-- <el-icon
                              v-if="stageViewMode === 'planned'"
                              class="edit-icon"
                              size="12"
                            >
                              <Edit />
                            </el-icon> -->
                          </div>
                          <div v-else class="stage-title-edit">
                            <el-input
                              v-model="stage.editingStage"
                              size="small"
                              @blur="saveTitle(index)"
                              @keyup.enter="saveTitle(index)"
                              @keyup.esc="cancelEditTitle(index)"
                              ref="titleInputRefs"
                            />
                            <div class="edit-actions">
                              <el-icon class="save-icon" @click="saveTitle(index)">
                                <Check />
                              </el-icon>
                              <el-icon class="cancel-icon" @click="cancelEditTitle(index)">
                                <Close />
                              </el-icon>
                            </div>
                          </div>
                        </div>

                        <!-- 阶段日期 -->
                        <div class="stage-date-row">
                          <div
                            v-if="!stage.isEditingDate"
                            class="stage-date"
                            @click="startEditDate(index)"
                          >
                            {{ stage.date }}
                            <el-icon
                              v-if="stageViewMode === 'planned'"
                              class="edit-icon"
                              size="12"
                            >
                              <Edit />
                            </el-icon>
                          </div>
                          <div v-else class="stage-date-edit">
                            <el-date-picker
                              v-model="stage.editingDate"
                              type="date"
                              size="small"
                              format="YYYY年MM月DD日"
                              value-format="YYYY年MM月DD日"
                              @blur="saveDate(index)"
                              @change="saveDate(index)"
                              ref="dateInputRefs"
                            />
                            <div class="edit-actions">
                              <el-icon class="save-icon" @click="saveDate(index)">
                                <Check />
                              </el-icon>
                              <el-icon class="cancel-icon" @click="cancelEditDate(index)">
                                <Close />
                              </el-icon>
                            </div>
                          </div>
                        </div>

                        <!-- 阶段状态 -->
                        <div class="stage-status">
                          <el-tag
                            :type="getStageTagType(stage.status)"
                            size="small"
                          >
                            {{ getStageStatusText(stage.status) }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="合同管理" name="contract">
          <!-- 合同管理内容 -->
          <div class="contract-manage-container">
            <!-- 统计卡片 -->
            <div class="stats-cards">
              <div class="stats-card">
                <div class="stats-value">{{ formatAmount(contractManageData.stats.qualifiedAmount) }}</div>
                <div class="stats-label">含税合同总金额</div>
              </div>
              <div class="stats-card">
                <div class="stats-value">{{ formatAmount(contractManageData.stats.unqualifiedAmount) }}</div>
                <div class="stats-label">不含税合同总金额</div>
              </div>
            </div>

            <!-- 合同列表 -->
            <div class="contract-list-section">
              <div class="section-header">
                <div class="section-title">合同列表</div>
              </div>

              <im-table
                ref="contractTableRef"
                :data="contractManageData.list"
                :columns="contractColumns"
                :pagination="contractPagination"
                :loading="contractManageData.loading"
                :toolbar="contractToolbarConfig"
                :column-storage="createColumnStorage('contract_manage', 'local')"
                height="450px"
                stripe
                border
                center
                show-index
                show-overflow-tooltip
                @on-page-change="handleContractPageChange"
                @on-page-size-change="handleContractPageSizeChange"
                @sort-change="handleContractSortChange"
                @selection-change="handleContractSelectionChange"
              >
                <!-- 工具栏左侧 -->
                <!-- <template #toolbar-left="{ checkedRows }">
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="checkedRows.length === 0"
                    @click="handleContractBatchExport(checkedRows)"
                  >
                    批量导出
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    :disabled="checkedRows.length === 0"
                    @click="handleContractBatchDelete(checkedRows)"
                  >
                    批量删除
                  </el-button>
                </template> -->

                <!-- 工具栏右侧 -->
                <!-- <template #toolbar-right>
                  <el-button
                    type="success"
                    size="small"
                    @click="handleContractAdd"
                  >
                    新增合同
                  </el-button>
                  <el-button
                    size="small"
                    @click="handleContractExportAll"
                  >
                    导出全部
                  </el-button>
                </template> -->

                <!-- 操作列 -->
                <template #operator="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleContractViewDetail(row)"
                  >
                    查看明细
                  </el-button>
                  <!-- <el-button
                    type="warning"
                    size="small"
                    link
                    @click="handleContractEdit(row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="handleContractDelete(row)"
                  >
                    删除
                  </el-button> -->
                </template>



                <!-- 含税合同金额列 -->
                <template #totalContractAmount="{ row }">
                  {{ formatAmount(row.totalContractAmount) }}
                </template>

                <!-- 不含税合同金额列 -->
                <template #nonVatAmount="{ row }">
                  {{ formatAmount(row.nonVatAmount) }}
                </template>

                <!-- 增值税率列 -->
                <template #vatTaxRate="{ row }">
                  <span>{{ row.vatTaxRate }}%</span>
                </template>
              </im-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="变更管理" name="change">
          <!-- 变更管理内容 -->
          <ChangeManage
            :change-list="changeManageData.list"
            :pagination="changeManageData.pagination"
            :loading="changeManageData.loading"
            @page-change="handleChangePageChange"
            @sort-change="handleChangeSortChange"
            @selection-change="handleChangeSelectionChange"
            @view-detail="handleChangeViewDetail"
            @edit="handleChangeEdit"
            @delete="handleChangeDelete"
            @batch-delete="handleChangeBatchDelete"
            @batch-export="handleChangeBatchExport"
            @export-all="handleChangeExportAll"
            @add-change="handleChangeAdd"
          />
        </el-tab-pane>

        <el-tab-pane label="设备清单" name="equipment">
          <!-- 设备清单内容 -->
          <EquipmentList
            :supplier-material-list="equipmentListData.supplierMaterialList"
            :supplier-pagination="equipmentListData.supplierPagination"
            :supplier-loading="equipmentListData.supplierLoading"
            @supplier-page-change="handleSupplierPageChange"
            @supplier-page-size-change="handleSupplierPageSizeChange"
            @supplier-selection-change="handleSupplierSelectionChange"
            @supplier-view-detail="handleSupplierViewDetail"
            @supplier-batch-delete="handleSupplierBatchDelete"
            @supplier-batch-export="handleSupplierBatchExport"
            @supplier-export-all="handleSupplierExportAll"
            @supplier-add="handleSupplierAdd"
          />
        </el-tab-pane>

        <el-tab-pane label="工程量清单" name="engineering">
          <!-- 工程清单内容 -->
          <EngineeringList
            :project-id="projectId"
            :project-code="props.needsPassedData?.projectCode || projectData.projectCode || projectId"
            @cell-edit="handleEngineeringCellEdit"
            @row-add="handleEngineeringRowAdd"
            @row-delete="handleEngineeringRowDelete"
          />
        </el-tab-pane>

        <el-tab-pane label="项目结算管理" name="settlement">
          <!-- 项目结算管理内容 -->
          <SettlementManage
            :project-id="projectId"
            :project-code="props.needsPassedData?.projectCode || projectData.projectCode || projectId"
            @cell-edit="handleSettlementCellEdit"
            @row-add="handleSettlementRowAdd"
            @row-delete="handleSettlementRowDelete"
          />
        </el-tab-pane>

        <el-tab-pane label="转固清单" name="assets">
          <!-- 转固清单内容 -->
          <AssetsManage
            :project-id="projectId"
            :project-code="props.needsPassedData?.projectCode || projectData.projectCode || projectId"
            @cell-edit="handleAssetsCellEdit"
            @row-add="handleAssetsRowAdd"
            @row-delete="handleAssetsRowDelete"
          />
        </el-tab-pane>

        <el-tab-pane label="项目资料管理" name="completion">
          <!-- 项目资料管理内容 -->
          <DocumentManage
            :project-id="projectId"
            @cell-edit="handleDocumentCellEdit"
            @row-add="handleDocumentRowAdd"
            @row-delete="handleDocumentRowDelete"
          />
        </el-tab-pane>

        <el-tab-pane label="异常提醒" name="design">
          <!-- 异常提醒内容 -->
          <AlertManage
            :project-id="projectId"
            :project-code="props.needsPassedData?.projectCode || projectData.projectCode || projectId"
            @cell-edit="handleAlertCellEdit"
            @row-add="handleAlertRowAdd"
            @row-delete="handleAlertRowDelete"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, nextTick, h } from 'vue';
import { ElMessage } from 'element-plus';
import { Edit, Check, Close } from '@element-plus/icons-vue';
import { getProjectDetailViewData, getProjectDetailsByCode, getProjectPlannedStages, getProjectActualStages, getDesignInfo, exportProjectInfo, modifyProjectPhase, ProjectStageData, DesignInfoData, auditExceptionReminder } from './api';

import {
  getContractTotalAmount,
  getContractList,
  deleteContract,
  batchDeleteContracts,
  exportContracts,
  type ContractData as ContractDataType,
  type ContractQueryParams
} from './components/contractApi';
import {
  getChangeList,
  deleteChange,
  batchDeleteChanges,
  exportChanges,
  type ChangeData as ChangeDataType,
  type ChangeQueryParams
} from './components/ChangeManage/api';
import ChangeManage from './components/ChangeManage/index.vue';
import {
  getSupplierMaterialList,
  type SupplierMaterialData,
  type SupplierMaterialQueryParams
} from './components/EquipmentList/api';
import EquipmentList from './components/EquipmentList/index.vue';
import EngineeringList from './components/EngineeringList/index.vue';
import SettlementManage from './components/SettlementManage/index.vue';
import AssetsManage from './components/AssetsManage/index.vue';
import AlertManage from './components/AlertManage/index.vue';
import DocumentManage from './components/DocumentManage/index.vue';
import { createColumnStorage } from '@/components/ItsmCommon';

// Props
interface Props {
  projectId?: string;
  needsPassedData?: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'back': [];
  'export-info': [data: any];
}>();

// 响应式数据
const activeTab = ref('overview');
const projectStages = ref<ProjectStageData[]>([]);
const stageViewMode = ref<'planned' | 'actual'>('planned');

// 项目概况数据加载状态
const overviewLoading = ref(false);

// 项目进度详情相关数据 - 从接口获取
const progressData = ref({
  plannedStartDate: '',
  plannedEndDate: ''
});

// 附件数据 - 从接口获取
const progressAttachmentData = ref([]);

const progressAttachmentColumns = [
  { prop: 'fileName', label: '附件名称', minWidth: 200 },
  { prop: 'uploadTime', label: '附件上传时间', width: 180 },
  { prop: 'uploader', label: '上传人', width: 120 },
  { prop: 'uploadStatus', label: '上传环节', width: 120 },
  { prop: 'operation', label: '操作', width: 150, slot: 'operation' }
];

// 扩展的阶段数据类型
interface ExtendedStageData extends ProjectStageData {
  phaseKey?: string; // 阶段键名，用于接口调用
  isEditingTitle?: boolean;
  isEditingDate?: boolean;
  editingStage?: string;
  editingDate?: string;
  originalStage?: string;
  originalDate?: string;
}

// 计划阶段数据和实际阶段数据
const plannedStages = ref<ExtendedStageData[]>([]);
const actualStages = ref<ExtendedStageData[]>([]);

// 引用
const titleInputRefs = ref<any[]>([]);
const dateInputRefs = ref<any[]>([]);
const designInfo = ref<DesignInfoData>({
  designApprovalNumber: '',
  designApprovalAmount: '',
  designApprovalDate: '',
  designUnit: '',
  designPersonInCharge: '',
  designPersonMethod: '',
  designDate: '',
  designAmount: ''
});
const projectData = ref({
  projectName: '',
  projectCode: '',
  approvalNumber: '',
  location: '',
  constructionMethod: '',
  constructionNature: '',
  fundingSource: '',
  currentStage: '',
  availableBudget: '',
  constructionUnit: '',
  basicConstructionCode: '',
  constructionUnitMethod: '',
  projectType: '',
  isGeneralContract: '',
  projectManager: '',
  projectManagerMethod: '',
  constructionContent: '',
  constructionDetails: '',
  designBudget: '',
  currentBudget: '',
  newBudget: '',
  totalBudget: '',
  // 接口字段 - 根据最新接口字段更新
  prov: '', // 省分
  city: '', // 地市
  projectId: '', // 项目ID
  disYear: '', // 建设年份
  touziSubject: '', // 投资主体
  specA: '', // A级专业
  specB: '', // B级专业
  specC: '', // C级专业
  legalSubject: '', // 法人主体
  networkLevel: '', // 网络层面
  bulidScene: '', // 建设性质
  remandUnit: '', // 需求单位
  disDept: '', // 标准建设部门
  projectPolicy: '', // 实际部门
  projectTrial: '', // 项目审批方式
  isPurchase: '', // 建设方式
  manageType: '', // 项目管理类别
  postDisaster: '', // 项目专项标识
  managerName: '', // 项目经理姓名
  managerEmail: '', // 项目经理邮箱前缀
  lxDate: '', // 立项批复时间
  lxSn: '', // 立项批复文号
  lxTouZi: '', // 立项批复总投资（元）
  lxCapex: '', // 立项批复本年CAPEX（元）
  kySn: '', // 可研批复文号
  kyTouZi: '', // 可研批复总投资（元）
  kyCapex: '', // 可研批复本年CAPEX（元）
  sjSn: '', // 设计批复文号
  sjTouZi: '', // 设计批复总投资（元）
  sjCapex: '', // 设计批复本年CAPEX（元）
  proState: '', // 项目状态
  isValid: '', // 是否有效
  ytProjectId: '', // 待立项项目ID
  dkyProjectCode: '', // 关联可研项目编号
  dkyProjectName: '', // 关联可研项目名称
  dkyTouZi: '', // 关联可研项目立项批复总投资（元）
  firstAccDate: '', // 初验时间
  firstAccTouzi: '', // 初验验收总投资（元）
  finalAccDate: '', // 终验时间
  finalAccTouzi: '', // 终验验收总投资（元）
  lxBulidContent: '', // 立项建设规模信息
  lxtzTouZi: '', // 立项调整投资（元）
  lxtzDate: '', // 立项调整批复时间
  lxtzBulidContent: '', // 立项调整建设规模信息
  sjBulidContent: '', // 设计建设规模信息
  sjtzTouZi: '', // 设计调整投资（元）
  sjtzDate: '', // 设计调整批复时间
  sjtzBulidContent: '', // 设计调整建设规模信息
  mainbuildContent: '', // 主要建设内容
  buildScene: '', // 建设性质
  projectState: '', // 项目当前阶段
  createdBy: '', // 创建人
  createdTime: '', // 创建时间
  updatedBy: '', // 更新人
  updatedTime: '', // 更新时间
  isShow: '', // 是否展示到项目列表
  isHand: '', // 是否手动添加
  label: '', // 标签
  isDelivery: '', // 是否交付
  // 时间字段
  startkyDate: '', // 发起可研审批时间
  startSjDate: '', // 发起设计审批时间
  planStartDate: '', // 计划开工时间
  planEndDate: '', // 计划完工时间
  planFirstYsDate: '', // 计划初验时间
  planLastYsDate: '', // 计划终验时间
  planYsEndDate: '', // 计划验收完成时间
  kyDate: '', // 可研批复时间
  sjDate: '', // 设计批复时间
  actualStartDate: '', // 实际开工时间
  actualEndDate: '', // 实际完工时间
  actualFirstYsDate: '', // 实际初验时间
  actualLastYsDate: '', // 实际终验时间
  actualYsEndDate: '' // 实际验收完成时间
});

// 富文本框数据 - 从接口获取
const constructionDetailsText = ref('');

// 合同管理数据
const contractManageData = ref({
  stats: {
    qualifiedAmount: 0,
    unqualifiedAmount: 0
  },
  list: [] as ContractDataType[],
  pagination: {
    currentPage: 1,
    pageSize: 10,
    total: 0
  },
  loading: false,
  queryParams: {
    conditions: [],
    pageNum: 1,
    pageSize: 10
  } as ContractQueryParams
});

// 变更管理数据
const changeManageData = ref({
  list: [] as ChangeDataType[],
  pagination: {
    currentPage: 1,
    pageSize: 10,
    total: 0
  },
  loading: false,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectCode: '',
    sortField: '',
    sortOrder: 'asc' as const
  } as ChangeQueryParams
});

// 设备清单数据
const equipmentListData = ref({
  supplierMaterialList: [] as SupplierMaterialData[],
  supplierPagination: {
    currentPage: 1,
    pageSize: 10,
    total: 0
  },
  supplierLoading: false,
  supplierQueryParams: {
    projectCode: '',
    pageNum: '1',
    pageSize: '10'
  } as SupplierMaterialQueryParams
});



// 合同表格引用
const contractTableRef = ref();

// 合同工具栏配置
const contractToolbarConfig = ref(true);

// 合同分页配置
const contractPagination = computed(() => ({
  ...contractManageData.value.pagination,
  background: true,
  layout: 'total, sizes, prev, pager, next, jumper',
  pageSizes: [10, 20, 50, 100],
  hideOnEmptyData: false,
  align: 'right' as const
}));

// 合同表格列配置
const contractColumns = ref([
  {
    prop: 'contractName',
    label: '合同名称',
    minWidth: 280,
    align: 'left',
    showOverflowTooltip: true,
    sortable: true,
    hidden: false
  },
  {
    prop: 'contractType',
    label: '合同类型',
    minWidth: 180,
    align: 'center',
    showOverflowTooltip: true,
    hidden: false
  },
  {
    prop: 'ourSigningParty',
    label: '我方签约主体',
    minWidth: 220,
    align: 'center',
    showOverflowTooltip: true,
    hidden: false
  },
  {
    prop: 'supplier1',
    label: '供应商',
    minWidth: 220,
    align: 'center',
    showOverflowTooltip: true,
    hidden: false
  },
  {
    prop: 'totalContractAmount',
    label: '含税合同金额（元）',
    minWidth: 160,
    align: 'right',
    sortable: true,
    showOverflowTooltip: true,
    slot: 'totalContractAmount',
    hidden: false
  },
  {
    prop: 'vatTaxRate',
    label: '增值税率（%）',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    slot: 'vatTaxRate',
    hidden: false
  },
  {
    prop: 'nonVatAmount',
    label: '不含税合同金额（元）',
    minWidth: 160,
    align: 'right',
    sortable: true,
    showOverflowTooltip: true,
    slot: 'nonVatAmount',
    hidden: false
  },
  {
    prop: 'operator',
    label: '操作',
    minWidth: 180,
    align: 'center',
    slot: 'operator',
    fixed: 'right',
    hidden: false
  }
]);

// 当前显示的阶段数据
const currentStageData = computed(() => {
  return stageViewMode.value === 'planned' ? plannedStages.value : actualStages.value;
});

// 计算项目ID
const projectId = computed(() => {
  return props.projectId || props.needsPassedData?.projectId || props.needsPassedData?.projectCode || projectData.value.projectCode;
});

// 格式化金额
const formatAmount = (amount: string | number) => {
  if (!amount) return '0';
  // 如果是字符串，先移除逗号再解析
  const num = typeof amount === 'string' ? parseFloat(amount.replace(/,/g, '')) : amount;
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 将日期转换为接口需要的格式 (YYYY-MM-DD)
const convertDateToApiFormat = (dateStr: string): string => {
  if (!dateStr) return '';

  try {
    // 如果是 "YYYY年MM月DD日" 格式，转换为 YYYY-MM-DD
    if (dateStr.includes('年') && dateStr.includes('月') && dateStr.includes('日')) {
      const year = dateStr.match(/(\d{4})年/)?.[1];
      const month = dateStr.match(/(\d{1,2})月/)?.[1];
      const day = dateStr.match(/(\d{1,2})日/)?.[1];

      if (year && month && day) {
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
    }

    // 如果已经是标准格式或其他格式，尝试解析
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0]; // YYYY-MM-DD
    }

    return dateStr; // 如果无法解析，返回原始字符串
  } catch (error) {
    console.error('日期格式转换失败:', error);
    return dateStr;
  }
};

// 获取阶段标签类型
const getStageTagType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    'normal': 'success',
    'overdue': 'danger',
    'pending': 'info'
  };
  return statusMap[status] || 'info';
};

// 获取项目阶段标签类型
const getProjectStageTagType = (stage: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const stageMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    // 实际已知的阶段
    '建设中': 'primary',      // 蓝色 - 表示正在进行中
    '设计阶段': 'warning',    // 橙色 - 表示设计准备阶段

    // 其他可能的阶段（保留以备扩展）
    '立项批复': 'info',
    '可研批复': 'info',
    '设计批复': 'warning',
    '工程施工': 'primary',
    '初验': 'warning',
    '终验': 'success',
    '验收交付': 'success',
    '交付结算': 'success',
    '已完成': 'success',
    '进行中': 'primary',
    '待开始': 'info',
    '暂停': 'warning',
    '异常': 'danger'
  };
  return stageMap[stage] || 'info';
};

// 获取阶段指示器样式类
const getStageIndicatorClass = (status: string) => {
  return {
    'normal': status === 'normal',
    'overdue': status === 'overdue',
    'pending': status === 'pending'
  };
};

// 获取阶段状态文本
const getStageStatusText = (status: string) => {
  const statusTextMap: Record<string, string> = {
    'normal': '正常',
    'overdue': '超期',
    'pending': '待开始'
  };
  return statusTextMap[status] || status;
};

// 阶段编辑方法
const startEditTitle = async (index: number) => {
  if (stageViewMode.value !== 'planned') return;

  const stage = plannedStages.value[index];
  stage.isEditingTitle = true;
  stage.editingStage = stage.stage;
  stage.originalStage = stage.stage;

  await nextTick();
  if (titleInputRefs.value[index]) {
    titleInputRefs.value[index].focus();
  }
};

const saveTitle = (index: number) => {
  const stage = plannedStages.value[index];
  if (stage.editingStage && stage.editingStage.trim()) {
    stage.stage = stage.editingStage.trim();
  }
  stage.isEditingTitle = false;
  stage.editingStage = '';
  stage.originalStage = '';
};

const cancelEditTitle = (index: number) => {
  const stage = plannedStages.value[index];
  stage.isEditingTitle = false;
  stage.editingStage = '';
  if (stage.originalStage) {
    stage.stage = stage.originalStage;
  }
  stage.originalStage = '';
};

const startEditDate = async (index: number) => {
  if (stageViewMode.value !== 'planned') return;

  const stage = plannedStages.value[index];
  stage.isEditingDate = true;
  stage.editingDate = stage.date;
  stage.originalDate = stage.date;

  await nextTick();
  if (dateInputRefs.value[index]) {
    dateInputRefs.value[index].focus();
  }
};

const saveDate = async (index: number) => {
  const stage = plannedStages.value[index];
  if (!stage.editingDate) {
    cancelEditDate(index);
    return;
  }

  try {
    // 获取项目编码
    const projectCode = props.needsPassedData?.projectCode || projectData.value.projectCode;

    if (!projectCode) {
      ElMessage.warning('项目编码为空，无法保存修改');
      cancelEditDate(index);
      return;
    }

    if (!stage.phaseKey) {
      ElMessage.warning('阶段键名为空，无法保存修改');
      cancelEditDate(index);
      return;
    }

    // 转换日期格式为 YYYY-MM-DD
    const formattedDate = convertDateToApiFormat(stage.editingDate);

    ElMessage.info('正在保存阶段时间...');

    // 调用修改接口
    const response = await modifyProjectPhase(projectCode, stage.phaseKey, formattedDate);

    if (response.status === '0') {
      // 保存成功，更新本地数据
      stage.date = stage.editingDate;
      stage.isEditingDate = false;
      stage.editingDate = '';
      stage.originalDate = '';
      ElMessage.success('阶段时间修改成功');
    } else {
      throw new Error(response.msg || '修改失败');
    }
  } catch (error) {
    console.error('保存阶段时间失败:', error);
    ElMessage.error('保存阶段时间失败');
    // 恢复原始值
    cancelEditDate(index);
  }
};

const cancelEditDate = (index: number) => {
  const stage = plannedStages.value[index];
  stage.isEditingDate = false;
  stage.editingDate = '';
  if (stage.originalDate) {
    stage.date = stage.originalDate;
  }
  stage.originalDate = '';
};

// 合同管理方法
const loadContractData = async () => {
  try {
    contractManageData.value.loading = true;

    // 获取项目编码
    const projectCode = props.needsPassedData?.projectCode || projectData.value.projectCode;

    if (projectCode) {
      try {
        // 使用真实接口加载统计数据
        const stats = await getContractTotalAmount(projectCode);
        contractManageData.value.stats = stats;
        console.log('合同总金额统计数据加载成功:', stats);
      } catch (error) {

      }

      // 设置查询条件，使用项目编码
      contractManageData.value.queryParams.conditions = [
        {
          value: projectCode,
          field: "projectCode",
          fuzzyable: true,
          operator: "fuzzy"
        }
      ];

      // 加载列表数据
      console.log('发送合同列表请求参数:', contractManageData.value.queryParams);
      const listResponse = await getContractList(contractManageData.value.queryParams);
      if (listResponse.status === '0') {
        contractManageData.value.list = listResponse.data.list;
        contractManageData.value.pagination.total = listResponse.data.total;
        console.log('合同列表数据加载成功:', {
          total: listResponse.data.total,
          pageNum: listResponse.data.pageNum,
          pageSize: listResponse.data.pageSize,
          listCount: listResponse.data.list.length
        });
      } else {
        throw new Error(listResponse.msg || '获取合同列表失败');
      }
    } else {
      console.warn('项目编码为空，无法加载合同数据');
      ElMessage.warning('项目编码为空，无法加载合同数据');
    }

  } catch (error) {
    console.error('加载合同数据失败:', error);
    ElMessage.error('加载合同数据失败');
  } finally {
    contractManageData.value.loading = false;
  }
};

const handleContractPageChange = async (pageOrSize: number, pageSize: number) => {
  console.log('合同分页变化 - 参数:', { pageOrSize, pageSize });
  console.log('合同分页变化 - 当前状态:', {
    oldPageNum: contractManageData.value.queryParams.pageNum,
    oldPageSize: contractManageData.value.queryParams.pageSize
  });

  // 根据im-table源码，这个事件会在页码变化和分页大小变化时都触发
  // 判断是页码变化还是分页大小变化
  if (pageOrSize !== contractManageData.value.queryParams.pageSize) {
    // 页码变化
    console.log('合同页码变化:', pageOrSize);
    contractManageData.value.queryParams.pageNum = pageOrSize;
    contractManageData.value.pagination.currentPage = pageOrSize;

    console.log('合同页码变化 - 更新后状态:', {
      newPageNum: contractManageData.value.queryParams.pageNum,
      newPageSize: contractManageData.value.queryParams.pageSize
    });

    await loadContractData();
  } else {
    // 分页大小变化，但不在这里处理，避免重复调用
    console.log('合同分页大小变化(在page-change中)，跳过处理');
  }
};

const handleContractSortChange = async (sortField: string, sortOrder: string) => {
  // 排序功能暂时不实现，因为新接口结构不同
  console.log('合同排序功能暂未实现:', { sortField, sortOrder });
  // contractManageData.value.queryParams.pageNum = 1;
  // await loadContractData();
};

// 合同相关辅助方法
const getContractStatusTagType = (status: string) => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'danger'> = {
    'active': 'success',
    'inactive': 'info',
    'pending': 'warning',
    'completed': 'success'
  };
  return statusMap[status] || 'info';
};

const getContractStatusText = (status: string) => {
  const statusTextMap: Record<string, string> = {
    'active': '活跃',
    'inactive': '非活跃',
    'pending': '待处理',
    'completed': '已完成'
  };
  return statusTextMap[status] || status;
};

// 移除不再使用的getContractPerformanceClass函数

const handleContractPageSizeChange = async (currentPage: number, newPageSize: number) => {
  console.log('合同页面大小变化 - 参数:', { currentPage, newPageSize });
  console.log('合同页面大小变化 - 当前状态:', {
    oldPageNum: contractManageData.value.queryParams.pageNum,
    oldPageSize: contractManageData.value.queryParams.pageSize
  });

  // 页面大小变化时，重置到第一页
  contractManageData.value.queryParams.pageSize = newPageSize;
  contractManageData.value.queryParams.pageNum = 1; // 固定重置到第一页
  contractManageData.value.pagination.pageSize = newPageSize;
  contractManageData.value.pagination.currentPage = 1; // 固定重置到第一页

  console.log('合同页面大小变化 - 更新后状态:', {
    newPageNum: contractManageData.value.queryParams.pageNum,
    newPageSize: contractManageData.value.queryParams.pageSize
  });

  await loadContractData();
};

const handleContractSelectionChange = (selectedRows: ContractDataType[]) => {
  console.log('选中的合同:', selectedRows);
};

const handleContractViewDetail = (row: ContractDataType) => {
  ElMessage.info(`查看合同详情：${row.contractName}`);
  // 这里可以实现查看合同详情的功能
  console.log('查看合同详情', row);
};

const handleContractEdit = (row: ContractDataType) => {
  ElMessage.info(`编辑合同：${row.contractName}`);
  // 这里可以实现编辑合同的功能
  console.log('编辑合同', row);
};

const handleContractDelete = async (row: ContractDataType) => {
  try {
    await deleteContract(row.id);
    ElMessage.success('删除成功');
    await loadContractData();
  } catch (error) {
    console.error('删除合同失败:', error);
    ElMessage.error('删除合同失败');
  }
};

const handleContractBatchDelete = async (rows: ContractDataType[]) => {
  try {
    const ids = rows.map(row => row.id);
    await batchDeleteContracts(ids);
    ElMessage.success(`成功删除 ${rows.length} 个合同`);
    await loadContractData();
  } catch (error) {
    console.error('批量删除合同失败:', error);
    ElMessage.error('批量删除合同失败');
  }
};

const handleContractBatchExport = async (rows: ContractDataType[]) => {
  try {
    const ids = rows.map(row => row.id);
    const result = await exportContracts(ids);
    if (result.success) {
      ElMessage.success('导出成功');
      // 这里可以处理下载逻辑
    }
  } catch (error) {
    console.error('批量导出合同失败:', error);
    ElMessage.error('批量导出合同失败');
  }
};

const handleContractExportAll = async () => {
  try {
    const result = await exportContracts();
    if (result.success) {
      ElMessage.success('导出成功');
      // 这里可以处理下载逻辑
    }
  } catch (error) {
    console.error('导出全部合同失败:', error);
    ElMessage.error('导出全部合同失败');
  }
};

const handleContractAdd = () => {
  ElMessage.info('新增合同功能开发中...');
  // 这里可以实现新增合同的功能
};

// 变更管理相关方法
const loadChangeData = async () => {
  // 确保有项目编码才加载数据
  const projectCode = props.needsPassedData?.projectCode || projectData.value.projectCode;
  if (!projectCode) {
    console.warn('变更管理: 项目编码为空，跳过数据加载');
    return;
  }

  changeManageData.value.loading = true;
  try {
    // 更新查询参数中的项目编码
    changeManageData.value.queryParams.projectCode = projectCode;

    console.log('变更管理: 开始加载数据，参数:', changeManageData.value.queryParams);

    const response = await getChangeList(changeManageData.value.queryParams);
    if (response.success) {
      changeManageData.value.list = response.rows;
      changeManageData.value.pagination.total = response.total;
      console.log('变更管理: 数据加载成功，共', response.total, '条记录');
    }
  } catch (error) {
    ElMessage.error('加载变更数据失败');
    console.error('变更管理: 加载数据失败:', error);
  } finally {
    changeManageData.value.loading = false;
  }
};

const handleChangePageChange = async (pageOrSize: number, pageSize: number) => {
  console.log('变更分页变化 - 参数:', { pageOrSize, pageSize });
  // 根据im-table源码，这个事件会在页码变化和分页大小变化时都触发
  // 判断是页码变化还是分页大小变化
  if (pageOrSize !== changeManageData.value.queryParams.pageSize) {
    // 页码变化
    console.log('变更页码变化:', pageOrSize);
    changeManageData.value.queryParams.pageNum = pageOrSize;
    changeManageData.value.pagination.currentPage = pageOrSize;
    await loadChangeData();
  } else {
    // 分页大小变化，但不在这里处理，避免重复调用
    console.log('变更分页大小变化(在page-change中)，跳过处理');
  }
};

const handleChangePageSizeChange = async (currentPage: number, size: number) => {
  console.log('变更页面大小变化 - 参数:', { currentPage, size });
  changeManageData.value.queryParams.pageSize = size;
  changeManageData.value.queryParams.pageNum = 1; // 重置到第一页
  changeManageData.value.pagination.pageSize = size;
  changeManageData.value.pagination.currentPage = 1; // 重置到第一页
  await loadChangeData();
};

const handleChangeSortChange = async (sortField: string, sortOrder: string) => {
  changeManageData.value.queryParams.sortField = sortField;
  changeManageData.value.queryParams.sortOrder = sortOrder as 'asc' | 'desc';
  changeManageData.value.queryParams.pageNum = 1;
  await loadChangeData();
};

const handleChangeSelectionChange = (selectedRows: ChangeDataType[]) => {
  // 处理选择变化
  console.log('Selected changes:', selectedRows);
};

const handleChangeViewDetail = (row: ChangeDataType) => {
  ElMessage.info(`查看变更详情: ${row.changeName}`);
};

const handleChangeEdit = (row: ChangeDataType) => {
  ElMessage.info(`编辑变更: ${row.changeName}`);
};

const handleChangeDelete = async (row: ChangeDataType) => {
  try {
    const response = await deleteChange(row.id);
    if (response.success) {
      ElMessage.success('删除成功');
      await loadChangeData();
    }
  } catch (error) {
    ElMessage.error('删除失败');
    console.error('Delete change error:', error);
  }
};

const handleChangeBatchDelete = async (rows: ChangeDataType[]) => {
  try {
    const ids = rows.map(row => row.id);
    const response = await batchDeleteChanges(ids);
    if (response.success) {
      ElMessage.success(`成功删除 ${rows.length} 条记录`);
      await loadChangeData();
    }
  } catch (error) {
    ElMessage.error('批量删除失败');
    console.error('Batch delete changes error:', error);
  }
};

const handleChangeBatchExport = async (rows: ChangeDataType[]) => {
  try {
    const ids = rows.map(row => row.id);
    const response = await exportChanges(ids);
    if (response.success) {
      ElMessage.success('导出成功');
    }
  } catch (error) {
    ElMessage.error('导出失败');
    console.error('Export changes error:', error);
  }
};

const handleChangeExportAll = async () => {
  try {
    const response = await exportChanges();
    if (response.success) {
      ElMessage.success('导出成功');
    }
  } catch (error) {
    ElMessage.error('导出失败');
    console.error('Export all changes error:', error);
  }
};

const handleChangeAdd = () => {
  ElMessage.success('新增变更功能开发中...');
};

// 设备清单相关方法
const loadSupplierMaterialData = async () => {
  equipmentListData.value.supplierLoading = true;
  try {
    const response = await getSupplierMaterialList(equipmentListData.value.supplierQueryParams);
    if (response.status === '0') {
      equipmentListData.value.supplierMaterialList = response.data.records;
      equipmentListData.value.supplierPagination.total = response.data.total;
    }
  } catch (error) {
    ElMessage.error('加载甲供物料数据失败');
    console.error('Load supplier material data error:', error);
  } finally {
    equipmentListData.value.supplierLoading = false;
  }
};

const handleSupplierPageChange = async (pageOrSize: number, pageSize: number) => {
  console.log('设备清单分页变化 - 参数:', { pageOrSize, pageSize });
  // 根据im-table源码，这个事件会在页码变化和分页大小变化时都触发
  // 判断是页码变化还是分页大小变化
  if (pageOrSize !== parseInt(equipmentListData.value.supplierQueryParams.pageSize)) {
    // 页码变化
    console.log('设备清单页码变化:', pageOrSize);
    equipmentListData.value.supplierQueryParams.pageNum = pageOrSize.toString();
    equipmentListData.value.supplierPagination.currentPage = pageOrSize;
    await loadSupplierMaterialData();
  } else {
    // 分页大小变化，但不在这里处理，避免重复调用
    console.log('设备清单分页大小变化(在page-change中)，跳过处理');
  }
};

const handleSupplierPageSizeChange = async (currentPage: number, size: number) => {
  console.log('设备清单页面大小变化 - 参数:', { currentPage, size });
  equipmentListData.value.supplierQueryParams.pageSize = size.toString();
  equipmentListData.value.supplierQueryParams.pageNum = '1'; // 重置到第一页
  equipmentListData.value.supplierPagination.pageSize = size;
  equipmentListData.value.supplierPagination.currentPage = 1; // 重置到第一页
  await loadSupplierMaterialData();
};

const handleSupplierViewDetail = (row: SupplierMaterialData) => {
  ElMessage.info(`查看甲供物料详情: ${row.deviceName}`);
};

// 设备清单选择事件
const handleSupplierSelectionChange = (selectedRows: SupplierMaterialData[]) => {
  console.log('Selected supplier materials:', selectedRows);
};

// 甲供物料清单批量操作
const handleSupplierBatchDelete = async (rows: SupplierMaterialData[]) => {
  try {
    ElMessage.success(`成功删除 ${rows.length} 条甲供物料记录`);
    await loadSupplierMaterialData();
  } catch (error) {
    ElMessage.error('批量删除甲供物料失败');
    console.error('Batch delete supplier materials error:', error);
  }
};

const handleSupplierBatchExport = async (rows: SupplierMaterialData[]) => {
  try {
    ElMessage.success('甲供物料批量导出成功');
  } catch (error) {
    ElMessage.error('甲供物料批量导出失败');
    console.error('Export supplier materials error:', error);
  }
};

const handleSupplierExportAll = async () => {
  try {
    ElMessage.success('甲供物料全部导出成功');
  } catch (error) {
    ElMessage.error('甲供物料全部导出失败');
    console.error('Export all supplier materials error:', error);
  }
};

const handleSupplierAdd = () => {
  ElMessage.success('新增甲供物料功能开发中...');
};



// 工程量清单相关方法
const handleEngineeringCellEdit = (rowIndex: number, columnKey: string, value: any) => {
  console.log('Engineering cell edit:', { rowIndex, columnKey, value });
  ElMessage.info(`编辑第${rowIndex + 1}行的${columnKey}字段`);
};

const handleEngineeringRowAdd = () => {
  ElMessage.success('新增工程量清单行功能开发中...');
};

const handleEngineeringRowDelete = (rowIndex: number) => {
  ElMessage.success(`删除第${rowIndex + 1}行功能开发中...`);
};

// 项目结算管理相关方法
const handleSettlementCellEdit = (rowIndex: number, columnKey: string, value: any) => {
  console.log('Settlement cell edit:', { rowIndex, columnKey, value });
  ElMessage.info(`编辑第${rowIndex + 1}行的${columnKey}字段`);
};

const handleSettlementRowAdd = () => {
  ElMessage.success('新增结算记录功能开发中...');
};

const handleSettlementRowDelete = (rowIndex: number) => {
  ElMessage.success(`删除第${rowIndex + 1}行功能开发中...`);
};

// 转固清单相关方法
const handleAssetsCellEdit = (rowIndex: number, columnKey: string, value: any) => {
  console.log('Assets cell edit:', { rowIndex, columnKey, value });
  ElMessage.info(`编辑第${rowIndex + 1}行的${columnKey}字段`);
};

const handleAssetsRowAdd = () => {
  ElMessage.success('新增转固清单记录功能开发中...');
};

const handleAssetsRowDelete = (rowIndex: number) => {
  ElMessage.success(`删除第${rowIndex + 1}行功能开发中...`);
};

// 异常提醒相关方法
const handleAlertCellEdit = (rowIndex: number, columnKey: string, value: any) => {
  console.log('Alert cell edit:', { rowIndex, columnKey, value });
  ElMessage.info(`编辑第${rowIndex + 1}行的${columnKey}字段`);
};

const handleAlertRowAdd = () => {
  ElMessage.success('新增异常提醒记录功能开发中...');
};

const handleAlertRowDelete = (rowIndex: number) => {
  ElMessage.success(`删除第${rowIndex + 1}行功能开发中...`);
};

// 项目资料管理相关方法
const handleDocumentCellEdit = (rowIndex: number, columnKey: string, value: any) => {
  console.log('Document cell edit:', { rowIndex, columnKey, value });
  ElMessage.info(`编辑第${rowIndex + 1}行的${columnKey}字段`);
};

const handleDocumentRowAdd = () => {
  ElMessage.success('新增项目资料记录功能开发中...');
};

const handleDocumentRowDelete = (rowIndex: number) => {
  ElMessage.success(`删除第${rowIndex + 1}行功能开发中...`);
};

// 项目进度详情相关方法
const handleProgressSubmit = () => {
  ElMessage.success('项目进度详情提交成功');
};

const handleProgressSave = () => {
  ElMessage.success('项目进度详情保存成功');
};

const handleProgressPreview = (row: any) => {
  // 导入并打开附件预览弹窗
  import('@/components/AttachmentPreview/index.vue').then((module: any) => {
    const AttachmentPreview = module.default || module;
    import('@/components/ReDialog').then(({ addDialog }) => {
      addDialog({
        title: '关联事件单',
        width: '800px',
        contentRenderer: () => h(AttachmentPreview, {
          attachmentId: row.id
        }),
        hideFooter: true
      });
    });
  });
};

const handleProgressDownload = (row: any) => {
  ElMessage.success(`下载文件: ${row.fileName}`);
};

// 方法
const handleBack = () => {
  emit('back');
};

const handleExportInfo = async () => {
  try {
    // 获取项目编码和项目ID
    const projectCode = props.needsPassedData?.projectCode || projectData.value.projectCode;
    const projectIdValue = props.needsPassedData?.projectId || projectData.value.projectId || props.projectId;

    if (!projectCode) {
      ElMessage.warning('项目编码为空，无法导出项目信息');
      return;
    }

    console.log('导出项目信息参数:', { projectCode, projectId: projectIdValue });

    ElMessage.info('正在导出项目信息，请稍候...');

    // 调用导出接口，传递项目编码和项目ID
    await exportProjectInfo(projectCode, projectIdValue);

    ElMessage.success('项目信息导出成功');
  } catch (error) {
    console.error('导出项目信息失败:', error);
    ElMessage.error('导出项目信息失败');
  }
};

/**
 * 加载项目概况数据
 * @param projectCode 项目编码
 */
const loadProjectOverviewData = async (projectCode: string) => {
  if (!projectCode) {
    console.warn('ProjectDetailView: 项目编码为空，跳过概况数据加载');
    return;
  }

  overviewLoading.value = true;
  try {
    console.log('ProjectDetailView: 开始加载项目概况数据，projectCode:', projectCode);

    // 调用新的接口获取项目详情
    const apiData = await getProjectDetailsByCode(projectCode);

    // 更新项目数据，但保留已有的传递数据
    Object.keys(apiData).forEach(key => {
      if (apiData[key] && apiData[key] !== '') {
        projectData.value[key] = apiData[key];
      }
    });

    // 更新富文本框内容 - 优先使用主要建设内容
    if (apiData.constructionDetails) {
      constructionDetailsText.value = apiData.constructionDetails;
    } else if (projectData.value.mainbuildContent) {
      constructionDetailsText.value = projectData.value.mainbuildContent;
    } else if (projectData.value.sjBulidContent) {
      constructionDetailsText.value = projectData.value.sjBulidContent;
    }

    // 更新项目进度时间数据
    if (projectData.value.planStartDate) {
      progressData.value.plannedStartDate = projectData.value.planStartDate;
    }
    if (projectData.value.planEndDate) {
      progressData.value.plannedEndDate = projectData.value.planEndDate;
    }

    console.log('ProjectDetailView: 项目概况数据加载完成', projectData.value);
  } catch (error) {
    console.error('ProjectDetailView: 加载项目概况数据失败:', error);
    ElMessage.error('加载项目概况数据失败');
  } finally {
    overviewLoading.value = false;
  }
};

/**
 * 加载项目阶段数据
 * @param projectCode 项目编码
 */
const loadProjectStagesData = async (projectCode: string) => {
  if (!projectCode) {
    console.warn('ProjectDetailView: 项目编码为空，跳过阶段数据加载');
    return;
  }

  try {
    console.log('ProjectDetailView: 开始加载项目阶段数据，projectCode:', projectCode);

    // 加载项目计划阶段数据
    const plannedStagesData = await getProjectPlannedStages(projectCode);
    plannedStages.value = plannedStagesData.map(stage => ({
      ...stage,
      phaseKey: (stage as any).phaseKey, // 确保 phaseKey 被传递
      isEditingTitle: false,
      isEditingDate: false,
      editingStage: '',
      editingDate: '',
      originalStage: '',
      originalDate: ''
    }));

    // 加载项目实际阶段数据
    const actualStagesData = await getProjectActualStages(projectCode);
    actualStages.value = actualStagesData.map(stage => ({
      ...stage,
      phaseKey: (stage as any).phaseKey, // 确保 phaseKey 被传递
      isEditingTitle: false,
      isEditingDate: false,
      editingStage: '',
      editingDate: '',
      originalStage: '',
      originalDate: ''
    }));

    // 设置默认的项目阶段数据（用于兼容）
    projectStages.value = plannedStagesData;

    console.log('ProjectDetailView: 项目阶段数据加载完成', {
      plannedStages: plannedStages.value.length,
      actualStages: actualStages.value.length
    });
  } catch (error) {
    console.error('ProjectDetailView: 加载项目阶段数据失败:', error);
    ElMessage.error('加载项目阶段数据失败');
  }
};

// 监听阶段视图模式变化，清除编辑状态
watch(stageViewMode, () => {
  // 清除所有编辑状态
  plannedStages.value.forEach(stage => {
    stage.isEditingTitle = false;
    stage.isEditingDate = false;
    stage.editingStage = '';
    stage.editingDate = '';
    stage.originalStage = '';
    stage.originalDate = '';
  });
});

// 监听 props 变化
watch(() => props.needsPassedData, async (newData, oldData) => {
  if (newData && typeof newData === 'object') {
    console.log('ProjectDetailView: 接收到传递的数据:', newData);

    // 根据传递的数据设置默认标签页
    if (newData.defaultTab) {
      activeTab.value = newData.defaultTab;
      console.log('ProjectDetailView: 设置activeTab为', newData.defaultTab);
    } else {
      activeTab.value = 'overview';
      console.log('ProjectDetailView: 重置activeTab为overview');
    }

    projectData.value = {
      ...projectData.value,
      ...newData
    };
    console.log('ProjectDetailView: 更新后的项目数据:', projectData.value);

    // 如果项目编码发生变化，重新加载项目数据
    const newProjectCode = newData.projectCode;
    const oldProjectCode = oldData?.projectCode;
    if (newProjectCode && newProjectCode !== oldProjectCode) {
      console.log('ProjectDetailView: 项目编码变化，重新加载数据');
      await loadProjectOverviewData(newProjectCode);
      await loadProjectStagesData(newProjectCode);
    }
  }
}, { immediate: true, deep: true });

// 初始化数据
onMounted(async () => {
  console.log('ProjectDetailView: 组件挂载，props:', props);

  // 强制重置activeTab到默认值
  activeTab.value = 'overview';
  console.log('ProjectDetailView: 组件挂载时重置activeTab为overview');

  if (props.projectId || props.needsPassedData?.projectId) {
    const projectId = props.projectId || props.needsPassedData?.projectId;
    console.log('ProjectDetailView: 开始加载项目数据，projectId:', projectId);

    try {
      // 如果有传递的数据，先使用传递的数据
      if (props.needsPassedData && typeof props.needsPassedData === 'object') {
        console.log('ProjectDetailView: 使用传递的数据更新项目信息');
        projectData.value = { ...projectData.value, ...props.needsPassedData };
      }

      // 获取项目编码，优先使用传递的数据
      const projectCode = props.needsPassedData?.projectCode || projectData.value.projectCode || projectId;

      // 如果有项目编码，加载项目数据
      if (projectCode) {
        await loadProjectOverviewData(projectCode);
        await loadProjectStagesData(projectCode);
      } else {
        // 如果没有项目编码，使用原有的模拟数据加载方式作为兜底
        const data = await getProjectDetailViewData(projectId);
        // 只更新空字段，保留传递过来的数据
        Object.keys(data).forEach(key => {
          if (!projectData.value[key] || projectData.value[key] === '') {
            projectData.value[key] = data[key];
          }
        });
      }

      // 加载设计信息数据
      const design = await getDesignInfo(projectId);
      designInfo.value = design;

      // 加载合同管理数据
      await loadContractData();

      // 加载变更管理数据
      await loadChangeData();

      // 加载设备清单数据
      // 使用已经获取的项目编号
      equipmentListData.value.supplierQueryParams.projectCode = projectCode;
      await loadSupplierMaterialData();

      // 调用异常提醒接口
      if (projectCode) {
        try {
          await auditExceptionReminder(projectCode);
          console.log('ProjectDetailView: 异常提醒接口调用成功');
        } catch (error) {
          console.error('ProjectDetailView: 异常提醒接口调用失败:', error);
          // 不显示错误消息，静默处理
        }
      }

      console.log('ProjectDetailView: 所有数据加载完成');
    } catch (error) {
      console.error('ProjectDetailView: 加载数据时出错:', error);
      ElMessage.error('加载项目详情失败');
    }
  } else {
    console.log('ProjectDetailView: 没有提供projectId，跳过数据加载');
  }
});

// 定义组件名称
defineOptions({
  name: 'ProjectDetailView'
});
</script>

<style scoped lang="scss">
.project-detail-view-container {
  background: rgb(19, 24, 41);
  min-height: 100vh;
  padding: 24px;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .header-icon {
        font-size: 20px;
      }

      .header-title {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .project-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;

    .title-icon {
      font-size: 16px;
    }

    .title-text {
      color: #fff;
      font-size: 16px;
      font-weight: 500;
      flex: 1;
    }

    .project-stage-tag {
      margin-left: 12px;
      font-weight: 500;
    }
  }

  .tabs-container {
    .detail-tabs {
      :deep(.el-tabs__header) {
        margin-bottom: 20px;

        .el-tabs__nav-wrap {
          &::after {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }

        .el-tabs__item {
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;

          &.is-active {
            color: var(--el-color-primary);
          }

          &:hover {
            color: rgba(255, 255, 255, 0.9);
          }
        }

        .el-tabs__active-bar {
          background-color: var(--el-color-primary);
        }
      }

      :deep(.el-tabs__content) {
        .el-tab-pane {
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }

  .overview-section {
    .stats-cards {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 16px;
      margin-bottom: 32px;

      .stats-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 20px;
        text-align: center;

        .stats-value {
          color: #fff;
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .stats-label {
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
        }
      }
    }

    .main-content-layout {
      display: grid;
      grid-template-columns: 3fr 1fr;
      gap: 24px;
      margin-top: 24px;

      .left-content {
        .project-info-section {
          background: rgba(255, 255, 255, 0.02);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 24px;
        }
      }

      .right-content {
        .project-stages-section {
          background: rgba(255, 255, 255, 0.02);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 24px;
        }
      }
    }

    .project-info-section {
      .section-header {
        margin-bottom: 20px;

        .section-title {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
          padding-left: 8px;
          border-left: 3px solid var(--el-color-primary);
        }
      }

      .info-grid {
        padding: 0;

        .info-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 32px;
          margin-bottom: 16px;

          &.single-row {
            grid-template-columns: 1fr;
          }

          &.three-columns {
            grid-template-columns: 1fr 1fr 1fr;
            gap: 24px;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .info-item {
            display: flex;
            align-items: center;

            &.full-width {
              grid-column: 1 / -1;
            }

            .info-label {
              color: rgba(255, 255, 255, 0.7);
              font-size: 14px;
              min-width: 120px;
              flex-shrink: 0;
            }

            .info-value {
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
              flex: 1;
            }
          }
        }
      }

      .construction-content {
        margin-top: 24px;
        padding: 20px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.1);

        .content-row {
          display: flex;
          align-items: flex-start;
          gap: 16px;

          .content-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            min-width: 120px;
            flex-shrink: 0;
          }

          .content-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            line-height: 1.6;
            flex: 1;
          }
        }
      }

      .construction-details {
        margin-top: 16px;
        padding: 20px 0;

        .details-row {
          display: flex;
          align-items: flex-start;
          gap: 16px;

          .details-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            min-width: 120px;
            flex-shrink: 0;
            margin-top: 8px;
          }

          :deep(.el-textarea) {
            flex: 1;

            .el-textarea__inner {
              background-color: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
              line-height: 1.6;

              &:focus {
                border-color: var(--el-color-primary);
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
              }

              &::placeholder {
                color: rgba(255, 255, 255, 0.4);
              }
            }
          }
        }
      }

      .design-info-section {
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);

        .section-header {
          margin-bottom: 20px;

          .section-title {
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            padding-left: 8px;
            border-left: 3px solid var(--el-color-primary);
          }
        }
      }
    }

    .project-stages-section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .section-title {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
          padding-left: 8px;
          border-left: 3px solid var(--el-color-primary);
        }

        .stage-toggle {
          :deep(.el-radio-group) {
            .el-radio-button {
              .el-radio-button__inner {
                background-color: rgba(255, 255, 255, 0.05);
                border-color: rgba(255, 255, 255, 0.2);
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
                padding: 6px 12px;

                &:hover {
                  background-color: rgba(255, 255, 255, 0.1);
                  color: rgba(255, 255, 255, 0.9);
                }
              }

              &.is-active {
                .el-radio-button__inner {
                  background-color: var(--el-color-primary);
                  border-color: var(--el-color-primary);
                  color: #fff;
                }
              }

              &:first-child .el-radio-button__inner {
                border-left: 1px solid rgba(255, 255, 255, 0.2);
              }
            }
          }
        }
      }

      .stages-timeline {
        padding: 0;

        .stage-item {
          display: flex;
          align-items: flex-start;
          position: relative;

          &:not(:last-child) {
            margin-bottom: 24px;
          }

          .stage-indicator {
            position: relative;
            margin-right: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .stage-dot {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              background-color: rgba(255, 255, 255, 0.3);
              border: 2px solid rgba(255, 255, 255, 0.5);
              z-index: 2;
              transition: all 0.3s;

              &.normal {
                background-color: var(--el-color-success);
                border-color: var(--el-color-success);
              }

              &.overdue {
                background-color: var(--el-color-danger);
                border-color: var(--el-color-danger);
              }

              &.pending {
                background-color: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.5);
              }
            }

            .stage-line {
              width: 2px;
              height: 40px;
              background-color: rgba(255, 255, 255, 0.2);
              margin-top: 4px;
            }
          }

          .stage-content {
            flex: 1;
            padding-top: 2px;

            .stage-title-row {
              margin-bottom: 4px;

              .stage-title {
                color: rgba(255, 255, 255, 0.9);
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 2px 4px;
                border-radius: 4px;
                transition: all 0.2s;

                &:hover {
                  background-color: rgba(255, 255, 255, 0.05);

                  .edit-icon {
                    opacity: 1;
                  }
                }

                .edit-icon {
                  opacity: 0.7;
                  color: var(--el-color-primary);
                  transition: opacity 0.2s;
                }
              }

              .stage-title-edit {
                display: flex;
                align-items: center;
                gap: 8px;

                :deep(.el-input) {
                  .el-input__wrapper {
                    background-color: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.2);

                    .el-input__inner {
                      color: rgba(255, 255, 255, 0.9);
                      font-size: 14px;
                    }

                    &.is-focus {
                      border-color: var(--el-color-primary);
                      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
                    }
                  }
                }

                .edit-actions {
                  display: flex;
                  gap: 4px;

                  .el-icon {
                    cursor: pointer;
                    padding: 2px;
                    border-radius: 2px;
                    transition: all 0.2s;

                    &.save-icon {
                      color: var(--el-color-success);

                      &:hover {
                        background-color: rgba(103, 194, 58, 0.1);
                      }
                    }

                    &.cancel-icon {
                      color: var(--el-color-danger);

                      &:hover {
                        background-color: rgba(245, 108, 108, 0.1);
                      }
                    }
                  }
                }
              }
            }

            .stage-date-row {
              margin-bottom: 8px;

              .stage-date {
                color: rgba(255, 255, 255, 0.6);
                font-size: 12px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 2px 4px;
                border-radius: 4px;
                transition: all 0.2s;

                &:hover {
                  background-color: rgba(255, 255, 255, 0.05);

                  .edit-icon {
                    opacity: 1;
                  }
                }

                .edit-icon {
                  opacity: 0.7;
                  color: var(--el-color-primary);
                  transition: opacity 0.2s;
                }
              }

              .stage-date-edit {
                display: flex;
                align-items: center;
                gap: 8px;

                :deep(.el-date-editor) {
                  .el-input__wrapper {
                    background-color: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.2);

                    .el-input__inner {
                      color: rgba(255, 255, 255, 0.9);
                      font-size: 12px;
                    }

                    &.is-focus {
                      border-color: var(--el-color-primary);
                      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
                    }
                  }
                }

                .edit-actions {
                  display: flex;
                  gap: 4px;

                  .el-icon {
                    cursor: pointer;
                    padding: 2px;
                    border-radius: 2px;
                    transition: all 0.2s;

                    &.save-icon {
                      color: var(--el-color-success);

                      &:hover {
                        background-color: rgba(103, 194, 58, 0.1);
                      }
                    }

                    &.cancel-icon {
                      color: var(--el-color-danger);

                      &:hover {
                        background-color: rgba(245, 108, 108, 0.1);
                      }
                    }
                  }
                }
              }
            }

            .stage-status {
              display: inline-block;
            }
          }

          &.normal {
            .stage-indicator .stage-dot {
              background-color: var(--el-color-success);
              border-color: var(--el-color-success);
            }

            .stage-indicator .stage-line {
              background-color: var(--el-color-success);
            }
          }

          &.overdue {
            .stage-indicator .stage-dot {
              background-color: var(--el-color-danger);
              border-color: var(--el-color-danger);
            }
          }

          &.pending {
            .stage-indicator .stage-dot {
              background-color: rgba(255, 255, 255, 0.1);
              border-color: rgba(255, 255, 255, 0.3);
            }
          }
        }
      }
    }

    .design-section {
      .section-header {
        margin-bottom: 20px;

        .section-title {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
          padding-left: 8px;
          border-left: 3px solid var(--el-color-primary);
        }
      }

      .design-info-grid {
        background: rgba(255, 255, 255, 0.02);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 24px;

        .info-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 32px;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .info-item {
            display: flex;
            align-items: center;

            .info-label {
              color: rgba(255, 255, 255, 0.7);
              font-size: 14px;
              min-width: 140px;
              flex-shrink: 0;
            }

            .info-value {
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
              flex: 1;
            }
          }
        }
      }
    }
  }

  .section-header {
    margin-bottom: 20px;

    .section-title {
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      padding-left: 8px;
      border-left: 3px solid var(--el-color-primary);
    }
  }

  .placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;

    p {
      margin: 16px 0 0 0;
      font-size: 16px;
    }
  }

  // 合同管理样式
  .contract-manage-container {
    .stats-cards {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-bottom: 32px;

      .stats-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 20px;
        text-align: center;

        .stats-value {
          color: #fff;
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .stats-label {
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
        }
      }
    }

    .contract-list-section {
      .section-header {
        margin-bottom: 16px;

        .section-title {
          color: var(--el-color-primary);
          font-size: 14px;
          font-weight: 400;
          padding-left: 8px;
          border-left: 3px solid var(--el-color-primary);
        }
      }

      // 表格样式适配
      :deep(.im-table) {
        .el-table {
          background-color: transparent;

          th.el-table__cell {
            background-color: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-weight: 500;
            font-size: 14px;
          }

          td.el-table__cell {
            // background-color: transparent;
            color: rgba(255, 255, 255, 0.8);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            font-size: 13px;
          }

          .el-table__row {
            &:hover {
              td.el-table__cell {
                // background-color: rgba(255, 255, 255, 0.02);
              }
            }

            &.el-table__row--striped {
              td.el-table__cell {
                // background-color: rgba(255, 255, 255, 0.01);
              }

              &:hover {
                td.el-table__cell {
                  // background-color: rgba(255, 255, 255, 0.03);
                }
              }
            }
          }
        }

        .im-table-pagination {
          :deep(.el-pagination) {
            .el-pagination__total,
            .el-pagination__jump,
            .el-pager li {
              color: rgba(255, 255, 255, 0.8);
            }

            .el-pagination__sizes .el-select .el-input .el-input__wrapper {
              background-color: rgba(255, 255, 255, 0.05);
              border-color: rgba(255, 255, 255, 0.1);

              .el-input__inner {
                color: rgba(255, 255, 255, 0.8);
              }
            }

            .el-pager li {
              background-color: transparent;
              border: 1px solid rgba(255, 255, 255, 0.1);

              &.is-active {
                background-color: var(--el-color-primary);
                border-color: var(--el-color-primary);
                color: #fff;
              }

              &:hover {
                background-color: rgba(255, 255, 255, 0.05);
              }
            }

            .btn-prev,
            .btn-next {
              background-color: transparent;
              border: 1px solid rgba(255, 255, 255, 0.1);
              color: rgba(255, 255, 255, 0.8);

              &:hover {
                background-color: rgba(255, 255, 255, 0.05);
              }

              &:disabled {
                color: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.05);
              }
            }
          }
        }
      }
    }

    // 履约保证样式
    .performance-high {
      color: var(--el-color-success);
      font-weight: 600;
    }

    .performance-medium {
      color: var(--el-color-warning);
      font-weight: 500;
    }

    .performance-low {
      color: var(--el-color-danger);
      font-weight: 400;
    }
  }

  // 表格 tooltip 特殊样式适配 - 使用最强选择器
  :deep(.el-tooltip__popper),
  :deep(.el-tooltip__popper.is-dark),
  :deep(.el-tooltip__popper[data-popper-placement]),
  :deep(div.el-tooltip__popper),
  :deep([role="tooltip"]) {
    background: rgba(0, 0, 0, 0.9) !important;
    background-color: rgba(0, 0, 0, 0.9) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
    font-size: 12px;
    max-width: 300px;
    word-wrap: break-word;

    * {
      color: rgba(255, 255, 255, 0.9) !important;
    }

    .el-tooltip__arrow,
    .el-tooltip__arrow::before {
      background: rgba(0, 0, 0, 0.9) !important;
      background-color: rgba(0, 0, 0, 0.9) !important;
      border: 1px solid rgba(255, 255, 255, 0.1) !important;
      border-color: rgba(255, 255, 255, 0.1) !important;
    }
  }

  // 项目进度详情样式
  .progress-detail-container {
    .progress-steps {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
      padding: 0 20px;

      .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        &:not(:last-child)::after {
          content: '';
          position: absolute;
          top: 15px;
          left: 50%;
          width: 100px;
          height: 2px;
          background: rgba(255, 255, 255, 0.2);
          z-index: 1;
        }

        &.active {
          .step-number {
            background: #409eff;
            color: #ffffff;
          }

          .step-label {
            color: #409eff;
          }
        }

        .step-number {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          margin-bottom: 8px;
          position: relative;
          z-index: 2;
        }

        .step-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }

    .progress-info-card,
    .attachment-info-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
      border: 1px solid rgba(255, 255, 255, 0.1);

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 24px;

        .header-icon {
          width: 4px;
          height: 16px;
          background: #409eff;
          border-radius: 2px;
        }

        .header-text {
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
        }
      }

      .info-grid {
        .info-row {
          display: flex;
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .info-item {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 16px;

            &.date-item {
              align-items: flex-start;
              flex-direction: column;
              gap: 8px;
            }

            .label {
              min-width: 120px;
              color: rgba(255, 255, 255, 0.8);
              font-size: 14px;
            }

            .value {
              color: #ffffff;
              font-size: 14px;
            }

            .date-input {
              width: 100%;

              :deep(.date-picker) {
                width: 100%;

                .el-input__wrapper {
                  background-color: rgba(255, 255, 255, 0.1);
                  border: 1px solid rgba(255, 255, 255, 0.2);

                  &:hover {
                    border-color: #409eff;
                  }

                  &.is-focus {
                    border-color: #409eff;
                  }
                }

                .el-input__inner {
                  color: #ffffff;

                  &::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                  }
                }
              }
            }
          }
        }
      }

      .upload-section {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 24px;

        .upload-btn {
          min-width: 100px;
        }

        .upload-tip {
          color: rgba(255, 255, 255, 0.6);
          font-size: 12px;
        }
      }

      .attachment-table {
        :deep(.im-table) {
          .el-table {
            background-color: transparent;
            color: #ffffff;

            .el-table__header {
              background-color: rgba(255, 255, 255, 0.05);

              th {
                background-color: transparent;
                color: #ffffff;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
              }
            }

            .el-table__body {
              tr {
                background-color: transparent;

                &:hover {
                  background-color: rgba(255, 255, 255, 0.05);
                }

                td {
                  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                  color: #ffffff;
                }
              }
            }
          }
        }
      }
    }

    .progress-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding: 24px 0;

      .el-button {
        min-width: 100px;
      }
    }
  }

  // 项目阶段标签样式
  :deep(.el-tag) {
    &.el-tag--primary {
      background: rgba(64, 158, 255, 0.2);
      border-color: rgba(64, 158, 255, 0.3);
      color: #409eff;
    }

    &.el-tag--success {
      background: rgba(103, 194, 58, 0.2);
      border-color: rgba(103, 194, 58, 0.3);
      color: #67c23a;
    }

    &.el-tag--warning {
      background: rgba(230, 162, 60, 0.2);
      border-color: rgba(230, 162, 60, 0.3);
      color: #e6a23c;
    }

    &.el-tag--info {
      background: rgba(144, 147, 153, 0.2);
      border-color: rgba(144, 147, 153, 0.3);
      color: #909399;
    }

    &.el-tag--danger {
      background: rgba(245, 108, 108, 0.2);
      border-color: rgba(245, 108, 108, 0.3);
      color: #f56c6c;
    }
  }
}
</style>
